{"type": "page", "body": [{"type": "form", "name": "form2", "title": "删除推送表数据(erp_push_data)", "api": "../batchdb/deletePushData", "body": [{"label": "tenantId", "type": "input-text", "name": "tenantId"}, {"label": "count", "type": "static", "name": "count"}], "actions": [{"type": "submit", "label": "删除"}]}, {"type": "form", "name": "form2", "title": "将中间表数据修改为同步成功", "api": "../syncDataMapping/updateStatus2Success", "body": [{"label": "tenantId", "type": "input-text", "name": "tenantId"}, {"label": "sourceObjApiName", "type": "input-text", "name": "sourceObjApiName"}, {"label": "destObjApiName", "type": "input-text", "name": "destObjApiName"}, {"label": "仅isCreated=true的数据", "type": "checkbox", "value": true, "name": "onlyCreated"}, {"label": "最多查询数量", "type": "input-number", "name": "limit", "value": 1000}, {"label": "执行查询数据量", "type": "input-text", "static": true, "name": "totalQuery"}, {"label": "执行修改数据量", "type": "input-text", "static": true, "name": "totalUpdate"}], "actions": [{"type": "submit", "label": "执行"}]}]}