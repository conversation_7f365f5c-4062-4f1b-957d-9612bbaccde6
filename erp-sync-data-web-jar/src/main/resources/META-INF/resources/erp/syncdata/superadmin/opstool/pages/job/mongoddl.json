{"type": "page", "body": [{"type": "form", "title": "创建索引", "api": {"url": "../ddl/mongo/createIndexes", "method": "post", "responseData": {"result": "${a}"}}, "body": [{"type": "input-text", "name": "collection", "label": "集合前缀，结尾不需要带_"}, {"type": "input-text", "name": "type", "label": "类型（log或者temp),指定到哪个库"}, {"type": "input-text", "name": "indexName", "label": "索引名称"}, {"type": "input-text", "name": "partialFilterExpression", "label": "索引范围partialFilterExpression，可为空，示例：{\"operation_type\": null}"}, {"type": "input-text", "name": "index", "label": "索引key，map格式，如：{\"status\":1,\"isDeleted\":1,\"updateTime\":-1}"}, {"type": "input-text", "name": "tenantIdStr", "label": "限定企业（逗号分隔）"}, {"type": "checkbox", "name": "skipFailed", "label": "是否跳过失败的企业"}, {"type": "checkbox", "name": "dropIndexes", "label": "是否删除索引"}, {"type": "static", "name": "result", "visibleOn": "typeof data.result !== 'undefined'", "label": "结果"}]}]}