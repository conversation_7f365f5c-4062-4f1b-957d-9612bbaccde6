package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.admin.arg.CountMongoCollectionStats;
import com.fxiaoke.open.erpsyncdata.admin.arg.ResetSyncLogExpireTime;
import com.fxiaoke.open.erpsyncdata.admin.manager.MongoStoreManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseExpireIndexLogMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.ErpTempMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.reflections.Reflections.log;

/**
 * <AUTHOR>
 * @Date: 10:11 2021/6/29
 * @Desc:
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/mongo")
public class SuperAdminMongoStoreCollector {
    @Autowired
    private MongoStoreManager mongoStoreManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private List<BaseExpireIndexLogMongoStore> baseExpireIndexLogMongoStores;
    @Autowired
    private ErpTempMongoStore erpTempMongoStore;


    @RequestMapping("/getAllStoreCollections")
    @ResponseBody
    public List<String> getAllStoreCollections() {
        return mongoStoreManager.getAllStoreCollections();
    }

    @RequestMapping("/getAllStoreCollectionSize")
    @ResponseBody
    public Map<String, Long> getAllStoreCollectionSize() {
        return mongoStoreManager.getAllStoreCollectionSize();
    }

    @PostMapping("/resetSyncLogExpireTime")
    @ResponseBody
    public Result<Map<String, Map<String, String>>> resetSyncLogExpireTime(@RequestBody ResetSyncLogExpireTime.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return Result.newError("请先填写企业");   // ignoreI18n   实施和开发自用
        }

        if (Objects.nonNull(arg.getExpireDay()) && arg.getExpireDay() > 0) {
            for (String tenantId : arg.getTenantIds()) {
                tenantConfigurationManager.updateConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.SYNC_LOG_EXPIRE_TIME_TENANT_CONFIG.name(), String.valueOf(arg.getExpireDay()));
            }
        }

        Map<String, String> errorTenantMap = new HashMap<>();
        for (String tenantId : arg.getTenantIds()) {
            try {
                for (BaseExpireIndexLogMongoStore mongoStore : baseExpireIndexLogMongoStores) {
                    mongoStore.resetExpireTime(tenantId);
                }
            } catch (Exception e) {
                log.warn("重置企业{}的Mongo表过期时间失败", tenantId, e);
                errorTenantMap.put(tenantId, e.getMessage());
            }
        }

        return Result.newSuccess(ImmutableMap.of("data", errorTenantMap));
    }

    /**
     * 统计Mongo表统计
     */
    @PostMapping("/countMongoCollectionStats")
    @ResponseBody
    public Result<List<CountMongoCollectionStats.TenantCollStat>> countMongoCollectionStats(@RequestBody CountMongoCollectionStats.Arg arg) {
        Map<String, List<CollStat>> map = arg.getTenantIds().stream()
                .collect(Collectors.toMap(Function.identity(), tenantId -> new ArrayList<>()));

        if (BooleanUtils.isTrue(arg.getAllLog())) {
            map.forEach((id, stat) -> stat.addAll(baseExpireIndexLogMongoStores.stream()
                    .map(dao -> dao.getCollStat(id))
                    .collect(Collectors.toList())));
        }

        if (BooleanUtils.isTrue(arg.getTemp())) {
            map.forEach((id, stat) -> stat.add(erpTempMongoStore.getCollStat(id)));
        }

        final List<CountMongoCollectionStats.TenantCollStat> collect = map.entrySet().stream()
                .map(entry -> {
                    final CollStat reduce = entry.getValue().stream()
                            .reduce(new CollStat(), (o1, o2) -> {
                                final CollStat collStat = new CollStat();
                                collStat.setAvgObjSize((o1.getAvgObjSize() * o1.getCount() + o2.getAvgObjSize() * o2.getCount()) / (o1.getCount() + o2.getCount()));
                                collStat.setCount(o1.getCount() + o2.getCount());
                                collStat.setSize(o1.getSize() + o2.getSize());
                                collStat.setTotalIndexSize(o1.getTotalIndexSize() + o2.getTotalIndexSize());
                                collStat.setStorageSize(o1.getStorageSize() + o2.getStorageSize());
                                return collStat;
                            });
                    final CountMongoCollectionStats.TenantCollStat copy = BeanUtil.copy(reduce, CountMongoCollectionStats.TenantCollStat.class);
                    copy.setTenantId(entry.getKey());

                    return copy;
                }).collect(Collectors.toList());

        return Result.newSuccess(collect);
    }
}
