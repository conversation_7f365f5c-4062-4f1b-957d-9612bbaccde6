<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-erp-sync-data</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>sync-converter</artifactId>
  <packaging>jar</packaging>
  <dependencies>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-crm-rest-api</artifactId>
    </dependency>
      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>common-db-proxy</artifactId>
      </dependency>
      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-aviator</artifactId>
      </dependency>

      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-test</artifactId>
      </dependency>

      <!-- JUnit 5 dependencies -->
      <dependency>
          <groupId>org.junit.jupiter</groupId>
          <artifactId>junit-jupiter-api</artifactId>
          <scope>test</scope>
      </dependency>
      <dependency>
          <groupId>org.junit.jupiter</groupId>
          <artifactId>junit-jupiter-engine</artifactId>
          <scope>test</scope>
      </dependency>
      <!-- JUnit Vintage Engine (allows JUnit 4 tests to run via JUnit 5 platform) -->
      <dependency>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
          <scope>test</scope>
      </dependency>
      <dependency>
          <groupId>org.junit.jupiter</groupId>
          <artifactId>junit-jupiter-params</artifactId>
          <scope>test</scope>
      </dependency>
      <!-- Mockito for JUnit 5 -->
      <dependency>
          <groupId>org.mockito</groupId>
          <artifactId>mockito-junit-jupiter</artifactId>
          <scope>test</scope>
      </dependency>  </dependencies>
</project>