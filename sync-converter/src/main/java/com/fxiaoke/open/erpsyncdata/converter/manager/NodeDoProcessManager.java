package com.fxiaoke.open.erpsyncdata.converter.manager;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.SyncDataDependData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConvertFactory;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConverter;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FixedValueFieldConverter;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg.EventData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DefaultValueType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DetailObjectMappingsData.DetailObjectMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.*;
import com.fxiaoke.open.erpsyncdata.preprocess.util.SyncObjectFieldUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum.DEPEND_DATA__NOT_SYNC;

@Slf4j
@Component
public class NodeDoProcessManager extends AbsMainNodeProcessor {
    @Autowired
    private SyncDataMappingService syncDataMappingService;
    @Autowired
    private SyncMainService syncMainService;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private FieldConvertFactory fieldConvertFactory;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private OverrideOuterService overrideOuterService;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private DependDataSpecialService dependDataSpecialService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private CrmMetaManager crmMetaManager;
    @Autowired
    private ErpObjManager erpObjManager;

    public NodeDoProcessManager() {
        super(DataNodeNameEnum.DataFieldMapping);
    }

    @Override
    public boolean needProcess(SyncDataContextEvent ctx) {
        if (!super.needProcess(ctx)) {
            return false;
        }
        Integer sourceEventType = ctx.getSourceEventType();
        //保持原代码逻辑，除了作废，删除，恢复，都处理
        return !(
                EventTypeEnum.INVALID.match(sourceEventType)
                        || EventTypeEnum.DELETE_DIRECT.match(sourceEventType)
                        || EventTypeEnum.RECOVER.match(sourceEventType)
        );
    }

    @CompareSyncField(syncType = SyncCompareConstant.SYNC_DATA_CONVERT)
    public SyncDataContextEvent processMessage(final SyncDataContextEvent doProcessMqData) {
        log.info("DoProcessManager.processMessage,doProcessMqData={}", JSONObject.toJSONString(doProcessMqData));
        SyncDataData syncDataData = doProcessMqData.getSyncDataData();
        String tenantId = syncDataData.getTenantId();
        ObjectData destData = null;
        Integer destEventType = syncDataData.getDestEventType();
        Integer sourcetenantType = syncDataData.getSourceTenantType();
        Integer destTenantType = syncDataData.getDestTenantType();
        LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap = null;

        doProcessMqData.setTenantId(tenantId);
//        completeDataProcessArg.setDestMasterDataId(doProcessMqData.getDestMasterDataId());
//        completeDataProcessArg.setDestMasterObjectApiName(doProcessMqData.getDestMasterObjectApiName());

        doProcessMqData.setSyncDataId(syncDataData.getId());
        doProcessMqData.setSourceTenantId(syncDataData.getSourceTenantId());
        doProcessMqData.setDestObjectApiName(syncDataData.getDestObjectApiName());
        doProcessMqData.setDestEventType(syncDataData.getDestEventType());
        doProcessMqData.setDestTenantId(syncDataData.getDestTenantId());
        doProcessMqData.setDestTenantType(destTenantType);
        LinkedHashMap<String,String> destDetailObjMasterDetailFieldApiName= Maps.newLinkedHashMap();
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId,syncDataData.getSyncPloyDetailSnapshotId()).getData();
        SyncDataDependData syncDataDependData=SyncDataDependData.builder().tenantId(tenantId).objApiName(syncDataData.getSourceObjectApiName())
                .ployDetailId(syncPloyDetailSnapshotData.getSyncPloyDetailId())
                .locale(doProcessMqData.getLocale())
                .dataId(syncDataData.getSourceDataId()).syncDataId(syncDataData.getId()).dependMappingDataList(Lists.newArrayList()).build();
        try {
            SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotData.getSyncPloyDetailData();
            //主对象字段映射
            List<FieldMappingData> syncDataFieldMappings = null;
            //从对象字段映射
            List<DetailObjectMappingData> detailObjectMappings = null;
            Map<String, List<SyncDataData>> detailSyncDataMaps = new HashMap<>();
            //如果同步数据的对象apiName等于策略明细的对象apiName,说明是主对象
            if (syncPloyDetailData.getSourceObjectApiName().equals(syncDataData.getSourceObjectApiName())) {
                syncDataFieldMappings = syncPloyDetailData.getFieldMappings();
                detailObjectMappings = syncPloyDetailData.getDetailObjectMappings();
                for (Entry<String, List<String>> entry : syncDataData.getSourceDetailSyncDataIds().entrySet()) {
                    List<String> detailSyncDataIds = syncDataData.getSourceDetailSyncDataIds().get(entry.getKey());
                    if (detailSyncDataIds != null && !detailSyncDataIds.isEmpty()) {
                        List<SyncDataData> detailSyncDatas = syncDataManager.batchGet(tenantId, detailSyncDataIds, true);
                        //(从对象apiName,从对象数据集合)
                        detailSyncDataMaps.put(entry.getKey(), detailSyncDatas);
                    }
                }
            } else {
                //拿出对应从对象的映射
                for (DetailObjectMappingData detailObjectMapping : syncPloyDetailData.getDetailObjectMappings()) {
                    if (detailObjectMapping.getSourceObjectApiName().equals(syncDataData.getSourceObjectApiName())) {
                        syncDataFieldMappings = detailObjectMapping.getFieldMappings();
                        break;
                    }
                }
                detailObjectMappings = new ArrayList<>();
            }
            //处理依赖数据
            DependData dependData = doDependDatas(tenantId, syncDataData, syncDataFieldMappings, detailSyncDataMaps, detailObjectMappings,
                    syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncRules().getSyncDependForce(), syncDataDependData);
            //主对象数据转目标对象数据
            destData = doConvertToDestData(tenantId, syncDataData.getId(), syncDataData.getSourceData(), destEventType, syncDataData.getDestTenantId(), syncDataData.getDestObjectApiName(), syncDataFieldMappings, destTenantType, sourcetenantType);
            destData.putId(syncDataData.getDestDataId());
            //从对象数据转目标对象数据
            destDetailSyncDataIdAndDestDataMap = new LinkedHashMap<>();
            for (DetailObjectMappingData detailObjectMappingData : detailObjectMappings) {
                String sourceDetailObjectApiName = detailObjectMappingData.getSourceObjectApiName();
                String destDetailObjectApiName = detailObjectMappingData.getDestObjectApiName();
                List<FieldMappingData> masterFieldMappings=detailObjectMappingData.getFieldMappings().stream().filter(fieldMappingData -> FieldType.MASTER_DETAIL.equals(fieldMappingData.getSourceType())&&FieldType.MASTER_DETAIL.equals(fieldMappingData.getDestType())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(masterFieldMappings)){
                    destDetailObjMasterDetailFieldApiName.put(destDetailObjectApiName,masterFieldMappings.get(0).getDestApiName());
                }
                List<SyncDataData> souceDetailSyncDatas = detailSyncDataMaps.get(sourceDetailObjectApiName);
                if (souceDetailSyncDatas == null || souceDetailSyncDatas.isEmpty()) {
                    continue;
                }
                for (SyncDataData souceDetailSyncData : souceDetailSyncDatas) {
                    ObjectData souceDetailData = souceDetailSyncData.getSourceData();
                    ObjectData destDetailData = doConvertToDestData(tenantId, souceDetailSyncData.getId(), souceDetailData, destEventType, syncDataData.getDestTenantId(), destDetailObjectApiName,
                            detailObjectMappingData.getFieldMappings(), destTenantType, sourcetenantType);
                    destDetailData.putId(souceDetailSyncData.getDestDataId());
                    destDetailData.safePutFrom(destData, "owner");
                    destDetailData.safePutFrom(destData, "created_by");
                    destDetailSyncDataIdAndDestDataMap.put(souceDetailSyncData.getId(), destDetailData);
                }
            }
        } catch (SyncDataException sex) {
            saveDependData(tenantId, syncDataDependData);
            if(sex.getErrCode() != DEPEND_DATA__NOT_SYNC.getErrCode()) { //依赖数据报错的，用户的页面也会显示，就不用打印日志了
                log.info("doProcessMqData={},errorCode={},errorMsg={}", doProcessMqData, sex.getErrCode(), sex.getErrMsg());
                log.warn("doProcessMqData SyncDataException : ", sex);
            }
            doProcessMqData.setErrCode(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode());
            doProcessMqData.setErrMsg(sex.getErrMsg());
        } catch (Exception e) {
            log.warn("doProcessMqData=" + doProcessMqData, e);
            doProcessMqData.setErrCode(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode());
            doProcessMqData.setErrMsg(e.getMessage());
        }
        if (destData != null) {
            doProcessMqData.setDestDataId(destData.getId());
        }
        if(!doProcessMqData.isSuccess()&&destDetailSyncDataIdAndDestDataMap==null
                &&!CollectionUtils.isEmpty(syncDataData.getSourceDetailSyncDataIds())){//把明细的syncDataId加上，否则后续不处理为失败，只能等待超时
            destDetailSyncDataIdAndDestDataMap = new LinkedHashMap<>();
            for(String obj:syncDataData.getSourceDetailSyncDataIds().keySet()){
                if(CollectionUtils.isEmpty(syncDataData.getSourceDetailSyncDataIds().get(obj))){
                    continue;
                }
                for(String syncDataId:syncDataData.getSourceDetailSyncDataIds().get(obj)){
                    destDetailSyncDataIdAndDestDataMap.put(syncDataId,null);//失败的，后续不能处理value，因为value为空
                }
            }
        }
        doProcessMqData.setDestDetailObjMasterDetailFieldApiName(destDetailObjMasterDetailFieldApiName);
        doProcessMqData.setDestData(destData);
        doProcessMqData.setDestDetailSyncDataIdAndDestDataMap(destDetailSyncDataIdAndDestDataMap);
        doProcessMqData.setSyncPloyDetailSnapshotId(syncDataData.getSyncPloyDetailSnapshotId());
//        syncMainService.completeDataProcess(completeDataProcessArg);
        String lastNodeStatus=StringUtils.isBlank(doProcessMqData.getErrMsg())?i18NStringManager.getByEi(I18NStringEnum.s6, tenantId):doProcessMqData.getErrMsg();
        syncDataManager.updateNodeMsg(tenantId, null,PloyDetailNodeEnum.SYNC_DATA,lastNodeStatus,null);

        if (!doProcessMqData.isSuccess()) {
            updateToError(tenantId, doProcessMqData, SyncDataStatusEnum.BE_PROCESS.getStatus(), SyncDataStatusEnum.PROCESS_FAILED.getStatus());
            return doProcessMqData.stop(i18NStringManager.getByEi(I18NStringEnum.s444, tenantId));
        }
        return doProcessMqData.next();
    }


    private void updateToError(String tenantId, SyncDataContextEvent message, int status, int newStatus) {
        syncDataManager.updateToError(tenantId, message.getSyncDataId(), newStatus, message.getErrMsg(), String.valueOf(message.getErrCode()));
        Map<String, ObjectData> detailSyncMap = message.getDestDetailSyncDataIdAndDestDataMap();
        if (detailSyncMap != null) {
            for (Entry<String, ObjectData> entry : detailSyncMap.entrySet()) {//value可能为空，不要使用
                syncDataManager.updateToError(tenantId, entry.getKey(), newStatus, message.getErrMsg(), String.valueOf(message.getErrCode()));
            }
        }
    }

    private ObjectData doConvertToDestData(String tenantId, String syncDataId, ObjectData sourceData, Integer destEventType, String destTenantId, String destObjectApiName, List<FieldMappingData> fieldMappingDataList, Integer destTenantType, Integer sourceTenantType) {
        ObjectData destData = new ObjectData();
        destData.putApiName(destObjectApiName);
        destData.putTenantId(destTenantId);
        for (FieldMappingData fieldMappingData : fieldMappingDataList) {
           doConvertValue(tenantId, sourceData, destEventType, destTenantId, fieldMappingData, destTenantType, sourceTenantType, destObjectApiName,destData);
        }
        syncDataManager.updateSourceData(tenantId, syncDataId, sourceData,destEventType);//重新更新一般sourceData,有可能引用字段的sourceData被更新了
        Set<String> destApiNameList = fieldMappingDataList.stream().map(FieldMappingData::getDestApiName).collect(Collectors.toSet());
        if (destApiNameList.contains(ObjectDescribeContants.OUT_OWNER) && destData.get(ObjectDescribeContants.OUT_OWNER) != null) {
            String outerOwnerId = (destData.getOutOwner()).get(0);
            if (StringUtils.isNotEmpty(outerOwnerId)) {
                Long outerUid = Long.valueOf(outerOwnerId);
                Long outerTenantId = outerServiceFactory.get(sourceTenantType).getFsAccountByOuter(outerUid).getData().getOuterTenantId();
                destData.putOutTenantId(outerTenantId);
            }
        }
        log.debug("DoProcessManager.doConvertToDestData,destData={}",destData);
        return destData;
    }

    @Nullable
    private Object parseDefaultValue(String tenantId, ObjectData sourceData, Integer destEventType, String destTenantId, String destObjectApiName, Integer destTenantType, Integer sourceTenantType, FieldMappingData fieldMappingData) {
        Object newValue;
        //判断为null，且设置了默认值逻辑
        String defaultValue = fieldMappingData.getDefaultValue();
        DefaultValueType defaultValueType = DefaultValueType.parseFromValue(defaultValue);
        if (defaultValueType != DefaultValueType.FIX_VALUE) {
            //一些特殊取值
            newValue = defaultValueType.getSpecialValue();
        } else {
            //正常的默认值
            if ("null".equals(defaultValue)) {//如果默认值为null,设置为空串
                if (fieldMappingData.getDestType().equals(FieldType.EMPLOYEE)
                        || (fieldMappingData.getDestType().equals(FieldType.DEPARTMENT)
                        || fieldMappingData.getDestType().equals(FieldType.SELECT_MANY))) {
                    newValue = Lists.newArrayList();
                } else {
                    newValue = "";
                }
            } else {
                //这里直接使用固定值的处理
                FixedValueFieldConverter fixConverter = (FixedValueFieldConverter) fieldConvertFactory.getConverter(FieldMappingTypeEnum.FIXED_VAULE.getType());
                //无需深克隆
                FieldMappingData newFieldMappingData = BeanUtil.copy(fieldMappingData, FieldMappingData.class);
                newFieldMappingData.setMappingType(FieldMappingTypeEnum.FIXED_VAULE.getType());//固定值类型
                newFieldMappingData.setValue(defaultValue);
                newFieldMappingData.setDestType(fieldMappingData.getDestType());
                try {
                    newValue = fixConverter.convert(tenantId, sourceData, destEventType, destTenantId, newFieldMappingData, destTenantType, sourceTenantType, destObjectApiName);
                } catch (RuntimeException e) {
                    log.warn("doConvertValue error,sourceData={},fieldMappingData={}", sourceData, newFieldMappingData, e);
                    throw e;
                }
            }
        }
        return newValue;
    }

    private boolean isNum(String destType) {
        if (StringUtils.isBlank(destType)) {
            return false;
        }
        List<String> list = Lists.newArrayList(FieldType.CURRENCY, FieldType.NUMBER);
        return list.contains(destType);
    }

    private void doConvertValue(String tenantId, ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMappingData, Integer destTenantType, Integer sourceTenantType, String destObjectApiName,ObjectData destData) {
        if (EventTypeEnum.UPDATE.getType() == destEventType && BooleanUtils.isTrue(fieldMappingData.getNotUpdateField())) {
            //不执行更新
            return ;
        }
        if(FieldMappingTypeEnum.DEPARTMENT_EMPLOYEE_FROM_MAPPING_TABLE.getType() == fieldMappingData.getMappingType()) {
            if(StringUtils.equalsIgnoreCase(destObjectApiName,ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName())
                    || StringUtils.equalsIgnoreCase(destObjectApiName,ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName())) {
            } else {
                if (StringUtils.equalsIgnoreCase(fieldMappingData.getSourceType(), ErpFieldTypeEnum.object_reference_many.name())) {
                    fieldMappingData.setMappingType(FieldMappingTypeEnum.SOURCE_VALUE.getType());
                }
            }
        }
        try {
            //如果是CRM往ERP的，这个判断无意义，因为CRM的不存在key和null时相同意义
            boolean existKey = sourceData.containsKey(fieldMappingData.getSourceApiName());

            FieldConverter fieldConverter = fieldConvertFactory.getConverter(fieldMappingData.getMappingType());
            Object value = fieldConverter.convert(tenantId, sourceData, destEventType, tenantId, fieldMappingData, destTenantType, sourceTenantType, destObjectApiName);
            boolean isNull = value == null;
            if (isNull) {
                //空值的转换逻辑，，空值的判断逻辑在convert里面做了。
                if (ValueTypeEnum.DEFAULTVALUE.getType().equals(fieldMappingData.getValueType())) {
                    //默认值的处理，不存在不传值的情况
                    value = parseDefaultValue(tenantId, sourceData, destEventType, destTenantId, destObjectApiName, destTenantType, sourceTenantType, fieldMappingData);
                } else if (TenantType.ERP.equals(sourceTenantType) && !existKey) {
                    //对ERP的源数据，当转换值为null，且源数据不存在该字段时，不赋值到destData,即不修改目标数据的值。
                    boolean keepNullValue = tenantConfigurationManager.getKeepNullValueConfig(tenantId, destObjectApiName);
                    if (!keepNullValue) {
                        //只有这种情况， 不设置目标值,,,注意，如果设置了默认值，那么就一定会设置目标值
                        return;
                    }
                }
            }
            //设置目标值
            destData.put(fieldMappingData.getDestApiName(), value);
        } catch (RuntimeException e) {
            log.warn("doConvertValue error,sourceData={},fieldMappingData={}", sourceData, fieldMappingData, e);
            throw e;
        }
    }

    private DependData doDependDatas(String tenantId,
                                     SyncDataData syncDataData,
                                     List<FieldMappingData> syncDataFieldMappings,
                                     Map<String, List<SyncDataData>> detailObjectDataMaps,
                                     List<DetailObjectMappingData> detailObjectMappingDatas,
                                     Boolean syncDependForce,
                                     SyncDataDependData syncDataDependData) {
        DependData dependData = new DependData();
        List<EventData> sendEventDataList = new ArrayList<>();
        ObjectData sourceData = syncDataData.getSourceData();
        // 主对象或从对象（单独从对象处理）
        for (FieldMappingData fieldMapping : syncDataFieldMappings) {
            checkDependDatas(tenantId, syncDataData, syncDataDependData, dependData, sendEventDataList, sourceData, fieldMapping);
        }
        //从对象（主从对象一起处理才会走以下执行）
        for (DetailObjectMappingData detailObjectMappingData : detailObjectMappingDatas) {
            String sourceDetailObjectApiName = detailObjectMappingData.getSourceObjectApiName();
            List<SyncDataData> detailObjectDatas = detailObjectDataMaps.get(sourceDetailObjectApiName);
            if (detailObjectDatas == null) {
                continue;
            }
            for (SyncDataData detailSyncData : detailObjectDatas) {
                ObjectData detailObjectData = detailSyncData.getSourceData();
                for (FieldMappingData fieldMapping : detailObjectMappingData.getFieldMappings()) {
                    checkDependDatas(tenantId, syncDataData, syncDataDependData, dependData, sendEventDataList, detailObjectData, fieldMapping);
                }
            }
        }
        //去重
        sendEventDataList=sendEventDataList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() ->new TreeSet<>(Comparator.comparing(
                        u -> u.getSourceData().getTenantId() +";" + u.getSourceData().getApiName()+";" + u.getSourceData().getId()))), ArrayList::new));
        if (!CollectionUtils.isEmpty(sendEventDataList)) {
            Result2<List<EventData>> result = syncMainService.sendDependEventData(sendEventDataList, syncDataData.getDestTenantId(), syncDependForce);
            //result固定返回DEPEND_DATA__NOT_SYNC，里面的data跟sendEventDataList是一样的
            if (result.getIntErrCode() == DEPEND_DATA__NOT_SYNC.getErrCode()) {
                dependData.setFailEventDatas(result.getData());
                List<SyncDataDependData.DependMappingData> dependMappingDataList = syncDataDependData.getDependMappingDataList();
                dependMappingDataList = dependMappingDataList.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                                u -> u.getDependSourceApiName() + ";" + u.getDependDataId() + ";" + u.getDependDestApiName()))), ArrayList::new));
                if (!CollectionUtils.isEmpty(dependMappingDataList)) {
                    syncDataDependData.setDependMappingDataList(dependMappingDataList);
                }
            }
        }
        dependData.setEventDatas(sendEventDataList);
        //如果存在依赖数据映射未创建，当前数据不进入等待直接结束，保存一条saveDependData，定时任务定时去校验依赖数据映射是否已创建
        if (!CollectionUtils.isEmpty(syncDataDependData.getDependMappingDataList())) {
            log.info("syncDataDependData.getDependMappingDataList is {}", syncDataDependData.getDependMappingDataList());
            StringBuilder errorMsg = new StringBuilder();
            int i = 0;
            Boolean hasDataProcessFaild = false;
            for (SyncDataDependData.DependMappingData dependMappingData : syncDataDependData.getDependMappingDataList()) {
                if (dependMappingData.getException() != null) {
                    if (ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode() == dependMappingData.getException().getErrCode()) {
                        hasDataProcessFaild = true;
                    }
                    if (errorMsg.length() > 2000) {//限制一下字符
                        errorMsg.append(" ...");
                        continue;
                    }
                    i++;
                    errorMsg.append(i).append(".").append(String.format(dependMappingData.getException().getErrMsg(),dependMappingData.getDependFields().toString())).append("  ");
                }
            }
            if (hasDataProcessFaild) {//为了不改变后续的逻辑，还是区分一下两种异常，这种异常说明中间表存在映射，但是没有同步成功，当前数据不自动进入自动判断中间表的定时任务队列
                throw new SyncDataException(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(), errorMsg.toString());
            }
            //这种异常会把当前数据加入自动判断中间表的定时任务队列
            throw new SyncDataException(DEPEND_DATA__NOT_SYNC.getErrCode(), errorMsg.toString());
        }
        return dependData;
    }

    private void checkDependDatas(String tenantId, SyncDataData syncDataData, SyncDataDependData syncDataDependData, DependData dependData, List<EventData> sendEventDataList, ObjectData sourceData, FieldMappingData fieldMapping) {
        if (BooleanUtil.isTrue(fieldMapping.getUseSourceValueDirectly())) {
            //直接使用原值，不检查依赖
            return;
        }
        //非主从或查找关联类型数据不需要关注
        if ((fieldMapping.getMappingType() != FieldMappingTypeEnum.FIXED_VAULE.getType())
                && ((SyncObjectFieldUtil.isMasterDetailFieldType(fieldMapping.getSourceType())
                || SyncObjectFieldUtil.isObjectReferenceFieldType(fieldMapping.getSourceType())))
                && SyncObjectFieldUtil.isObjectReferenceFieldType(fieldMapping.getDestType())) {
            Object value = sourceData.get(fieldMapping.getSourceApiName());
            checkMappingIfExist(tenantId, syncDataData, syncDataDependData, dependData, sendEventDataList, fieldMapping, sourceData, value);
        }
        if(FieldType.OBJECT_REFERENCE_MANY.equals(fieldMapping.getSourceType())&&FieldType.OBJECT_REFERENCE_MANY.equals(fieldMapping.getDestType())) {
            Object value = sourceData.get(fieldMapping.getSourceApiName());
            if(value instanceof List) {
                List<Object> list = (List<Object>) value;
                for(Object obj : list) {
                    checkMappingIfExist(tenantId, syncDataData, syncDataDependData, dependData, sendEventDataList, fieldMapping, sourceData, obj);
                }
            }else {
                checkMappingIfExist(tenantId, syncDataData, syncDataDependData, dependData, sendEventDataList, fieldMapping, sourceData, value);
            }
            
        }
    }

    private void checkMappingIfExist(String tenantId, SyncDataData syncDataData, SyncDataDependData syncDataDependData, DependData dependData, List<EventData> sendEventDataList, FieldMappingData fieldMapping, ObjectData sourceData, Object value) {
        // 如果不强校验查找关联,不用校验
        if (!Objects.equals(fieldMapping.getDestType(), FieldType.MASTER_DETAIL) && BooleanUtils.isTrue(fieldMapping.getNotCheckMappingField())) {
            return;
        }

        if (value != null && StringUtils.isNotBlank(value.toString())) {
            //判断是否存在相同的依赖了
            for (SyncDataDependData.DependMappingData dependMappingData : syncDataDependData.getDependMappingDataList()) {
                if (fieldMapping.getSourceTargetApiName().equals(dependMappingData.getDependSourceApiName())
                        && value.toString().equals(dependMappingData.getDependDataId())
                        && fieldMapping.getDestTargetApiName().equals(dependMappingData.getDependDestApiName())){
                    dependMappingData.getDependFields().add(sourceData.getApiName()+":"+fieldMapping.getSourceApiName());
                    return;
                }
            }
            SyncDataMappingData dataMappingData = syncDataMappingService
                    .getSyncDataMapping(tenantId, syncDataData.getSourceTenantId(), fieldMapping.getSourceTargetApiName(), String.valueOf(value), syncDataData.getDestTenantId(),
                            fieldMapping.getDestTargetApiName()).getData();
            if(dataMappingData==null) {
                //如果不存在映射，且值是0或者0.0，不处理依赖，并且字段值改为空
                if ("0".equals(value.toString()) || "0.0".equals(value.toString())) {
                    sourceData.put(fieldMapping.getSourceApiName(), null);
                    return;
                }
                dataMappingData = dependDataSpecialService.getSyncDataMappingData(tenantId, sourceData, syncDataData, fieldMapping).getData();
            }
//            //映射为未创建成功
//            if (dataMappingData != null && !dataMappingData.getIsCreated()) {
//                dependData.setDepend(true);
//                String objectName = outerServiceFactory.get(syncDataData.getSourceTenantType()).getObjectNameByApiName(syncDataData.getSourceTenantId(), fieldMapping.getSourceTargetApiName()).getData();
//                SyncDataDependData.DependMappingData dependMappingData = SyncDataDependData.DependMappingData.builder().dependSourceApiName(fieldMapping.getSourceTargetApiName())
//                        .dependDataId(String.valueOf(value)).dependDestApiName(fieldMapping.getDestTargetApiName()).dependFields(Lists.newArrayList(sourceData.getApiName()+":"+fieldMapping.getSourceApiName())).build();
//                dependMappingData.setException(new SyncDataException(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(),
//                        I18NStringEnum.s5114.getText() +
//                        i18NStringManager.getByEi(I18NStringEnum.s904, tenantId) + fieldMapping.getSourceTargetApiName() + "（" + objectName + "）" +
//                                i18NStringManager.getByEi(I18NStringEnum.s905, tenantId) + value +"，" +
//                                i18NStringManager.getByEi(I18NStringEnum.s906, tenantId)));
//                syncDataDependData.getDependMappingDataList().add(dependMappingData);
//                return;
//                }

            //映射为空
            //相同方向，如果找到映射，但是映射报错，也要查下反方向集成流
            if (SyncObjectFieldUtil.isObjectsReferenceFieldType(fieldMapping.getDestType()) && (dataMappingData == null || !dataMappingData.getIsCreated())) {
                dependData.setDepend(true);
                EventData eventData = EventData.newAddEventData(sourceData.getTenantId(), fieldMapping.getSourceTargetApiName(), String.valueOf(value));
                //判断依赖数据是否有策略
                List<SyncPloyDetailSnapshotData2> list = syncPloyDetailSnapshotService.listNewestEnableSyncPloyDetailsSnapshots(sourceData.getTenantId(), fieldMapping.getSourceTargetApiName(), syncDataData.getSourceTenantType(), Lists.newArrayList(syncDataData.getDestTenantId())).getData();
                //如果有策略，先获取一次数据，如果为空，说明获取数据失败，直接报关联对象创建错误
                if (list.stream().anyMatch(data -> Objects.equals(data.getDestObjectApiName(), fieldMapping.getDestTargetApiName()))) {
                    Result2<ObjectData> objectDataResult = outerServiceFactory.get(syncDataData.getSourceTenantType()).getObjectData(tenantId, syncDataData.getSourceTenantType(), fieldMapping.getSourceTargetApiName(), String.valueOf(value),  true);
                    ObjectData objectData = objectDataResult.getData();
                    if (objectData == null) {
                        //还是把之前的报错信息打印出来
                        if (dataMappingData != null && !dataMappingData.getIsCreated()) {
                            dependData.setDepend(true);
                            String objectName = outerServiceFactory.get(syncDataData.getSourceTenantType()).getObjectNameByApiName(syncDataData.getSourceTenantId(), fieldMapping.getSourceTargetApiName()).getData();
                            SyncDataDependData.DependMappingData dependMappingData = SyncDataDependData.DependMappingData.builder().dependSourceApiName(fieldMapping.getSourceTargetApiName())
                                    .dependDataId(String.valueOf(value)).dependDestApiName(fieldMapping.getDestTargetApiName()).dependFields(Lists.newArrayList(sourceData.getApiName()+":"+fieldMapping.getSourceApiName())).build();
                            dependMappingData.setException(new SyncDataException(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(),
                                    I18NStringEnum.s5114.getText() +
                                            i18NStringManager.getByEi(I18NStringEnum.s904, tenantId) + fieldMapping.getSourceTargetApiName() + "（" + objectName + "）" +
                                            i18NStringManager.getByEi(I18NStringEnum.s905, tenantId) + value +"，" +
                                            i18NStringManager.getByEi(I18NStringEnum.s906, tenantId)));
                            syncDataDependData.getDependMappingDataList().add(dependMappingData);
                            return;
                        }

                        String objectName = outerServiceFactory.get(syncDataData.getSourceTenantType()).getObjectNameByApiName(syncDataData.getSourceTenantId(), fieldMapping.getSourceTargetApiName()).getData();
                        SyncDataDependData.DependMappingData dependMappingData = SyncDataDependData.DependMappingData.builder().dependSourceApiName(fieldMapping.getSourceTargetApiName())
                                .dependDataId(String.valueOf(value)).dependDestApiName(fieldMapping.getDestTargetApiName()).dependFields(Lists.newArrayList(sourceData.getApiName()+":"+fieldMapping.getSourceApiName())).build();
                        dependMappingData.setException(new SyncDataException(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(),
                                I18NStringEnum.s5114.getText() +
                                i18NStringManager.getByEi(I18NStringEnum.s904, tenantId) + fieldMapping.getSourceTargetApiName() + "（" + objectName + "）" +
                                        i18NStringManager.getByEi(I18NStringEnum.s907, tenantId) + value + "，"+
                                        i18NStringManager.getByEi(I18NStringEnum.s908, tenantId)+ objectName+
                                        i18NStringManager.getByEi(I18NStringEnum.s909, tenantId)+ value +
                                        i18NStringManager.getByEi(I18NStringEnum.s910, tenantId)));
                        syncDataDependData.getDependMappingDataList().add(dependMappingData);
                        return;
                    }
                    eventData.setSourceData(objectData);
                } else {
                    //还是把之前的报错信息打印出来
                    if (dataMappingData != null && !dataMappingData.getIsCreated()) {
                        dependData.setDepend(true);
                        String objectName = outerServiceFactory.get(syncDataData.getSourceTenantType()).getObjectNameByApiName(syncDataData.getSourceTenantId(), fieldMapping.getSourceTargetApiName()).getData();
                        SyncDataDependData.DependMappingData dependMappingData = SyncDataDependData.DependMappingData.builder().dependSourceApiName(fieldMapping.getSourceTargetApiName())
                                .dependDataId(String.valueOf(value)).dependDestApiName(fieldMapping.getDestTargetApiName()).dependFields(Lists.newArrayList(sourceData.getApiName()+":"+fieldMapping.getSourceApiName())).build();
                        dependMappingData.setException(new SyncDataException(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(),
                                I18NStringEnum.s5114.getText() +
                                        i18NStringManager.getByEi(I18NStringEnum.s904, tenantId) + fieldMapping.getSourceTargetApiName() + "（" + objectName + "）" +
                                        i18NStringManager.getByEi(I18NStringEnum.s905, tenantId) + value +"，" +
                                        i18NStringManager.getByEi(I18NStringEnum.s906, tenantId)));
                        syncDataDependData.getDependMappingDataList().add(dependMappingData);
                        return;
                    }

                    //如果没有策略，直接报关联对象创建错误
                    String objectName = outerServiceFactory.get(syncDataData.getSourceTenantType()).getObjectNameByApiName(syncDataData.getSourceTenantId(), fieldMapping.getSourceTargetApiName()).getData();
                    SyncDataDependData.DependMappingData dependMappingData = SyncDataDependData.DependMappingData.builder().dependSourceApiName(fieldMapping.getSourceTargetApiName())
                            .dependDataId(String.valueOf(value)).dependDestApiName(fieldMapping.getDestTargetApiName()).dependFields(Lists.newArrayList(sourceData.getApiName()+":"+fieldMapping.getSourceApiName())).build();
                    dependMappingData.setException(new SyncDataException(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(),
                            I18NStringEnum.s5114.getText() +
                            i18NStringManager.getByEi(I18NStringEnum.s904, tenantId) + fieldMapping.getSourceTargetApiName() + "（" + objectName + "）" +
                                    i18NStringManager.getByEi(I18NStringEnum.s911, tenantId) + value + "。"+
                                    i18NStringManager.getByEi(I18NStringEnum.s912, tenantId)+ value +
                                    i18NStringManager.getByEi(I18NStringEnum.s913, tenantId)));
                    syncDataDependData.getDependMappingDataList().add(dependMappingData);
                    return;

                }
                SyncDataDependData.DependMappingData dependMappingData = SyncDataDependData.DependMappingData.builder().dependSourceApiName(fieldMapping.getSourceTargetApiName())
                        .dependDataId(String.valueOf(value)).dependDestApiName(fieldMapping.getDestTargetApiName()).dependFields(Lists.newArrayList(sourceData.getApiName()+":"+fieldMapping.getSourceApiName())).build();
                StringBuilder errorMsg = new StringBuilder(I18NStringEnum.s5114.getText());
                errorMsg.append(i18NStringManager.getByEi(I18NStringEnum.s903,tenantId));
                String objectName = outerServiceFactory.get(syncDataData.getSourceTenantType()).getObjectNameByApiName(eventData.getSourceData().getTenantId(), eventData.getSourceData().getApiName()).getData();
                String dataName = StringUtils.isNotBlank(eventData.getSourceData().getName()) ? eventData.getSourceData().getName() : "";
                errorMsg.append("【").append(eventData.getSourceData().getApiName()).append("-").append(objectName).append(",").append(eventData.getSourceData().getId()).append(" ").append(dataName).append("】");
                dependMappingData.setException(new SyncDataException(ResultCodeEnum.DEPEND_DATA__NOT_SYNC.getErrCode(),errorMsg.toString()));
                syncDataDependData.getDependMappingDataList().add(dependMappingData);
                eventData.setSourceTenantType(syncDataData.getSourceTenantType());
                eventData.setSourceRemark(getDependDataRemark(syncDataData.getSourceTenantType(),tenantId,sourceData));
                dependData.setDepend(true);
                sendEventDataList.add(eventData);
            }
        }
    }

    private String getDependDataRemark(Integer sourceTenantType, String tenantId, ObjectData sourceData) {
        String name = sourceData.getName() == null ? sourceData.getId() : sourceData.getName();
        String objectApiName = sourceData.getApiName();
        StringBuilder sb = new StringBuilder();
        try {
            if (TenantType.CRM.equals(sourceTenantType)) {
                ObjectDescribe objectDescribe = crmMetaManager.getObjectDescribe(tenantId, objectApiName);
                if (objectDescribe == null) {
                    sb.append(objectApiName);
                } else {
                    sb.append(objectDescribe.getDisplayName());
                }
            } else {
                ErpObjectEntity erpObject = erpObjManager.getErpObj(tenantId, objectApiName);
                if (erpObject == null) {
                    sb.append(objectApiName);
                } else {
                    sb.append(erpObject.getErpObjectName());
                }
            }
        } catch (Exception e) {
            sb.append(objectApiName);
            log.warn("getDependDataRemark Exception e={}", e);
        }
        sb.append("[").append(name).append("]").append(I18NStringEnum.s682.getText());
        return sb.toString();
    }

    private void saveDependData(String tenantId, SyncDataDependData syncDataDependData) {
        if (syncDataDependData != null && !CollectionUtils.isEmpty(syncDataDependData.getDependMappingDataList())) {
            overrideOuterService.saveDependData(tenantId, syncDataDependData);
        }
    }
}
