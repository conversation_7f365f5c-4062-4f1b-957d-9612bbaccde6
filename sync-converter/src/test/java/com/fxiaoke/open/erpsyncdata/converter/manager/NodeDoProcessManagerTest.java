package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConvertFactory;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * NodeDoProcessManager 测试类
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
class NodeDoProcessManagerTest {

    @Mock
    private SyncDataMappingService syncDataMappingService;
    
    @Mock
    private SyncMainService syncMainService;
    
    @Mock
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    
    @Mock
    private FieldConvertFactory fieldConvertFactory;
    
    @Mock
    private OuterServiceFactory outerServiceFactory;
    
    @Mock
    private OverrideOuterService overrideOuterService;
    
    @Mock
    private SyncDataManager syncDataManager;
    
    @Mock
    private DependDataSpecialService dependDataSpecialService;
    
    @Mock
    private I18NStringManager i18NStringManager;
    
    @Mock
    private TenantConfigurationManager tenantConfigurationManager;
    
    @Mock
    private CrmMetaManager crmMetaManager;
    
    @Mock
    private ErpObjManager erpObjManager;

    @InjectMocks
    private NodeDoProcessManager nodeDoProcessManager;

    private SyncDataContextEvent testEvent;
    private SyncDataData testSyncDataData;
    private ObjectData testSourceData;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testSourceData = new ObjectData();
        testSourceData.put("id", "test-source-id");
        testSourceData.put("apiName", "test-api-name");
        testSourceData.put("tenantId", "test-tenant-id");
        testSourceData.put("field1", "value1");
        testSourceData.put("field2", "value2");

        testSyncDataData = new SyncDataData();
        testSyncDataData.setId("test-sync-data-id");
        testSyncDataData.setTenantId("test-tenant-id");
        testSyncDataData.setSourceData(testSourceData);
        testSyncDataData.setDestEventType(1);
        testSyncDataData.setSourceTenantType(1);
        testSyncDataData.setDestTenantType(2);
        testSyncDataData.setDestObjectApiName("dest-api-name");
        testSyncDataData.setDestDataId("dest-data-id");
        testSyncDataData.setSyncPloyDetailSnapshotId("snapshot-id");

        testEvent = new SyncDataContextEvent();
        testEvent.setSyncDataData(testSyncDataData);
        testEvent.setErrCode(ResultCodeEnum.SUCCESS.getErrCode());
        testEvent.setErrMsg(ResultCodeEnum.SUCCESS.getErrMsg());
    }

    @Test
    void testProcessMessage_Success() {
        // Given
        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");
        
        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(testEvent, result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
        
        // 验证依赖服务的调用
        verify(syncDataManager).updateNodeMsg(
            eq("test-tenant-id"), 
            isNull(), 
            any(), 
            eq("处理成功"), 
            isNull()
        );
    }

    @Test
    void testProcessMessage_WithSyncDataException() {
        // Given
        SyncDataException syncDataException = new SyncDataException(
            ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(), 
            "同步数据异常"
        );
        
        // 模拟在处理过程中抛出SyncDataException
        when(syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotData(anyString(), anyString()))
            .thenThrow(syncDataException);

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(), result.getErrCode());
        assertEquals("同步数据异常", result.getErrMsg());
    }

    @Test
    void testProcessMessage_WithGeneralException() {
        // Given
        RuntimeException generalException = new RuntimeException("系统异常");
        
        // 模拟在处理过程中抛出普通异常
        when(syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotData(anyString(), anyString()))
            .thenThrow(generalException);

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(), result.getErrCode());
        assertEquals("系统异常", result.getErrMsg());
    }

    @Test
    void testProcessMessage_WithNullSyncDataData() {
        // Given
        testEvent.setSyncDataData(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            nodeDoProcessManager.processMessage(testEvent);
        });
    }

    @Test
    void testProcessMessage_WithNullSourceData() {
        // Given
        testSyncDataData.setSourceData(null);

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        // 验证在源数据为null时的处理逻辑
    }

    @Test
    void testProcessMessage_WithEmptyTenantId() {
        // Given
        testSyncDataData.setTenantId("");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        // 验证租户ID为空时的处理逻辑
    }

    @Test
    void testProcessMessage_VerifyDependencyInteractions() {
        // Given
        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        nodeDoProcessManager.processMessage(testEvent);

        // Then
        // 验证各个依赖服务是否被正确调用
        verify(syncDataManager, times(1)).updateNodeMsg(
            anyString(), 
            isNull(), 
            any(), 
            anyString(), 
            isNull()
        );
        
        verify(i18NStringManager, times(1)).getByEi(
            eq(I18NStringEnum.s6), 
            eq("test-tenant-id")
        );
    }

    @Test
    void testProcessMessage_WithDifferentEventTypes() {
        // Given
        testSyncDataData.setDestEventType(2); // 不同的事件类型
        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
    }

    @Test
    void testProcessMessage_WithDifferentTenantTypes() {
        // Given
        testSyncDataData.setSourceTenantType(2);
        testSyncDataData.setDestTenantType(3);
        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
    }

    @Test
    void testProcessMessage_ErrorMessageHandling() {
        // Given
        testEvent.setErrMsg("已有错误信息");
        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        // 验证错误信息的处理逻辑
        verify(syncDataManager).updateNodeMsg(
            eq("test-tenant-id"),
            isNull(),
            any(),
            eq("已有错误信息"),
            isNull()
        );
    }

    @Test
    void testProcessMessage_WithComplexSourceData() {
        // Given - 复杂的源数据结构
        ObjectData complexSourceData = new ObjectData();
        testSourceData.put("id", "complex-id");
        testSourceData.put("apiName", "complex-api");
        testSourceData.put("tenantId", "test-tenant-id");
        complexSourceData.put("stringField", "测试字符串");
        complexSourceData.put("numberField", 12345);
        complexSourceData.put("booleanField", true);
        complexSourceData.put("nullField", null);
        complexSourceData.put("emptyField", "");

        testSyncDataData.setSourceData(complexSourceData);
        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
    }

    @Test
    void testProcessMessage_WithSpecialCharacters() {
        // Given - 包含特殊字符的数据
        testSourceData.put("specialChars", "测试@#$%^&*()中文字符");
        testSourceData.put("jsonString", "{\"key\":\"value\",\"number\":123}");
        testSourceData.put("sqlInjection", "'; DROP TABLE users; --");

        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
    }

    @Test
    void testProcessMessage_WithLargeDataSet() {
        // Given - 大数据集测试
        for (int i = 0; i < 1000; i++) {
            testSourceData.put("field" + i, "value" + i);
        }

        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
    }

    @Test
    void testProcessMessage_DependDataNotSyncException() {
        // Given - 测试依赖数据未同步异常
        SyncDataException dependException = new SyncDataException(
            ResultCodeEnum.DEPEND_DATA__NOT_SYNC.getErrCode(),
            "依赖数据未同步"
        );

        when(syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotData(anyString(), anyString()))
            .thenThrow(dependException);

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(), result.getErrCode());
        assertEquals("依赖数据未同步", result.getErrMsg());
    }

    @Test
    void testProcessMessage_MultipleExceptionScenarios() {
        // Given - 测试多种异常场景
        when(syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotData(anyString(), anyString()))
            .thenThrow(new IllegalArgumentException("参数错误"))
            .thenThrow(new NullPointerException("空指针异常"))
            .thenThrow(new RuntimeException("运行时异常"));

        // When & Then - 第一次调用
        SyncDataContextEvent result1 = nodeDoProcessManager.processMessage(testEvent);
        assertEquals(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(), result1.getErrCode());
        assertEquals("参数错误", result1.getErrMsg());

        // 重置事件状态
        testEvent.setErrCode(ResultCodeEnum.SUCCESS.getErrCode());
        testEvent.setErrMsg(ResultCodeEnum.SUCCESS.getErrMsg());

        // When & Then - 第二次调用
        SyncDataContextEvent result2 = nodeDoProcessManager.processMessage(testEvent);
        assertEquals(ResultCodeEnum.DATA_PROCESS_FAILDED.getErrCode(), result2.getErrCode());
        assertTrue(result2.getErrMsg().contains("空指针异常") || result2.getErrMsg() == null);
    }

    @Test
    void testProcessMessage_VerifyAllMockInteractions() {
        // Given
        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        nodeDoProcessManager.processMessage(testEvent);

        // Then - 验证所有Mock对象的交互
        verify(syncDataManager, atLeastOnce()).updateNodeMsg(
            anyString(),
            any(),
            any(),
            anyString(),
            any()
        );

        verify(i18NStringManager, atLeastOnce()).getByEi(
            any(I18NStringEnum.class),
            anyString()
        );

        // 验证没有调用不应该调用的方法
        verifyNoInteractions(fieldConvertFactory);
        verifyNoInteractions(outerServiceFactory);
        verifyNoInteractions(overrideOuterService);
        verifyNoInteractions(dependDataSpecialService);
        verifyNoInteractions(tenantConfigurationManager);
        verifyNoInteractions(crmMetaManager);
        verifyNoInteractions(erpObjManager);
    }

    @Test
    void testProcessMessage_BoundaryValues() {
        // Given - 边界值测试
        testSyncDataData.setDestEventType(Integer.MAX_VALUE);
        testSyncDataData.setSourceTenantType(Integer.MIN_VALUE);
        testSyncDataData.setDestTenantType(0);
        testSyncDataData.setTenantId(""); // 空字符串
        testSyncDataData.setDestObjectApiName(null); // null值

        when(i18NStringManager.getByEi(eq(I18NStringEnum.s6), anyString())).thenReturn("处理成功");

        // When
        SyncDataContextEvent result = nodeDoProcessManager.processMessage(testEvent);

        // Then
        assertNotNull(result);
        // 验证边界值处理
    }
}
