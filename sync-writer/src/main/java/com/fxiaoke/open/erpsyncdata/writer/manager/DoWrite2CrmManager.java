package com.fxiaoke.open.erpsyncdata.writer.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.contants.CheckinsFieldConstants;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.common.result.FindDetailDataListResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.*;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SyncCpqService;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.aop.SaveInterfaceMonitorAspect;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CompleteDataWriteArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.impl.SyncSkuSpuServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.NeedReturnData2SyncDataManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmRequestBaseParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.TriggerFlowConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.BatchCreateObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.BatchUpdateObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.service.*;
import com.fxiaoke.open.erpsyncdata.preprocess.util.BuriedSitesStatisticsUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.util.SyncObjectFieldUtil;
import com.fxiaoke.open.erpsyncdata.writer.manager.writecrm.SyncBomCoreObjManager;
import com.fxiaoke.open.erpsyncdata.writer.model.BatchDoWriteData;
import com.fxiaoke.open.erpsyncdata.writer.model.TeamMemberInfoData;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DoWrite2CrmManager {
    private static Set<String> userPaasAddApiNames = new HashSet<>();
    private static Set<String> userPaasUpdateApiNames = new HashSet<>();
    private static Set<String> syncDetailFillMasterUpdateApiNames = new HashSet<>();
    public static String fsPeerName = "erp-sync-data";
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private OverrideOuterService overrideOuterService;
    @Autowired
    private SyncSkuSpuServiceImpl skuSpuService;
    @Autowired
    private SyncStockService syncStockService;
    @Autowired
    private SyncOrgService syncOrgService;
    @Autowired
    private SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService;
    @Autowired
    private SyncCpqService syncCpqService;
    @Autowired
    private SyncMultipleUnitService syncMultipleUnitService;
    @Autowired
    private SpeedLimitManager speedLimitManager;
    @Autowired
    private SyncSerialNumberService syncSerialNumberService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private BatchWriteCRMManager batchWriteCRMManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private SyncBomCoreObjManager syncBomCoreObjManager;
    @Autowired
    private BuriedSitesStatisticsUtils buriedSitesStatisticsUtils;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private NeedReturnData2SyncDataManager needReturnData2SyncDataManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NodeSyncWriteMainManager syncWriteMainManager;
    @Autowired
    private NodeCompleteDataWriteManager completeDataWriteManager;
    @Autowired
    private MonitorReportManager monitorReportManager;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("erp-sync-data-all", config -> {
            String[] userPaasAddApiNamesArray = config.get("userPaasAddApiNames", "").split(",");
            Set<String> userPaasAddApiNamesTmp = new HashSet<>();
            for (String apiName : userPaasAddApiNamesArray) {
                userPaasAddApiNamesTmp.add(apiName);
            }
            userPaasAddApiNames = userPaasAddApiNamesTmp;

            String[] userPaasUpdateApiNamesArray = config.get("userPaasUpdateApiNames", "").split(",");
            Set<String> userPaasUpdateApiNamesTmp = new HashSet<>();
            for (String apiName : userPaasUpdateApiNamesArray) {
                userPaasUpdateApiNamesTmp.add(apiName);
            }
            userPaasUpdateApiNames = userPaasUpdateApiNamesTmp;

            String[] syncDetailFillMasterUpdateApiNamesArray = config.get("syncDetailFillMasterUpdateApiNames", "").split(",");
            Set<String> syncDetailFillMasterUpdateApiNamesTmp = new HashSet<>();
            for (String apiName : syncDetailFillMasterUpdateApiNamesArray) {
                syncDetailFillMasterUpdateApiNamesTmp.add(apiName);
            }
            syncDetailFillMasterUpdateApiNames = syncDetailFillMasterUpdateApiNamesTmp;
        });
    }
    // skipSend = "#syncDataContextEvent?.stop == false" 这里只上报批量写的数据，单条写的已经在objectDataService,上报了
    @DataMonitorScreen(tenantId = "#syncDataContextEvent.tenantId", dataCenterId = "#syncDataContextEvent.sourceDataCenterId", crmObjApiName = "#syncDataContextEvent.destData?.getApiName()", crmObjId = "#syncDataContextEvent?.destData?.getId()",historyDataType = "#syncDataContextEvent.dataReceiveType==3",sourceSystemType = "2",operationType = CommonConstant.WRITE_OPERATE_TYPE,skipSend = "#syncDataContextEvent?.stop == false",operateStatus="#syncDataContextEvent?.writeResult == null || #syncDataContextEvent.writeResult.isSuccess()?1:2")
    public void writeToCRM(final SyncDataContextEvent syncDataContextEvent) {
        log.info("write 2crm data:{}",syncDataContextEvent);
        String tenantId = syncDataContextEvent.getTenantId();

        beforeWrite(syncDataContextEvent);
        if (syncDataContextEvent.getStop()) {
            stopBeforeWrite(tenantId, syncDataContextEvent);
            return;
        }
        //这一句是原来的逻辑
        if (syncDataContextEvent.getDestEventType().equals(EventTypeEnum.ADD.getType()) && "DepartmentObj".equals(syncDataContextEvent.getDestObjectApiName())) {
            syncDataContextEvent.setDestDataId(null);
            syncDataContextEvent.getDestData().putId(null);
        }
        //发送到mq，调用批量写crm接口,后续消费完mq,要执行下面afterWrite逻辑
        Map<String, List<String>> batchWrite2Crm = configCenterConfig.getBatchWrite2CrmTenantAndObjApiName();
        //创建或者更新，包含了主对象，主从都会发生到mq,从对象也要加配置，存在单独更新从对象的
        boolean updateOrCreate = syncDataContextEvent.getDestEventType() == EventTypeEnum.ADD.getType() || syncDataContextEvent.getDestEventType() == EventTypeEnum.UPDATE.getType();
        boolean batchWrite = updateOrCreate && batchWrite2Crm.containsKey(tenantId)
                && CollectionUtils.isNotEmpty(batchWrite2Crm.get(tenantId))
                && batchWrite2Crm.get(tenantId).contains(syncDataContextEvent.getDestObjectApiName());
        if (batchWrite) {
            TimePointRecorderStatic.changeTimePointRecorder2OtherThread(syncDataContextEvent.getSyncDataId());
            batchWrite(syncDataContextEvent);
            return;
        }
        //正常的写逻辑
        SyncDataContextEvent doWriteResultDataResult = oneDataWrite(syncDataContextEvent);
        afterWrite(doWriteResultDataResult);
        syncDataManager.fillSimpleSyncData(syncDataContextEvent.getSyncDataMap(), doWriteResultDataResult);
        doWriteResultDataResult.next();
    }

    @CompareSyncField(syncType = SyncCompareConstant.BATCH_WRITE)
    private SyncDataContextEvent batchWrite(SyncDataContextEvent doWriteResultData) {
        Map<String, SyncDataEntity> syncDataMap = syncDataManager.getAndRemoveLocal();
        String syncDataEntityStr = JacksonUtil.toJson(syncDataMap.get(doWriteResultData.getSyncDataId()));
        doWriteResultData.setSyncDataEntityStr(syncDataEntityStr);
        doWriteResultData.setSyncLogBaseInfo(LogIdUtil.getBaseLogNoCreate());
        BatchDoWriteData batchDoWriteData = new BatchDoWriteData();
        batchDoWriteData.setMainContext(doWriteResultData);
        LinkedHashMap<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> destDetailSyncDataIdAndDestDataMap =
                doWriteResultData.getDestDetailSyncDataIdAndDestDataMap();
        if (destDetailSyncDataIdAndDestDataMap != null) {
            doWriteResultData.setDestDetailSyncDataIdAndDestDataMap(null);
            batchDoWriteData.setDetailContextList(new ArrayList<>());
            for (String syncDataId : destDetailSyncDataIdAndDestDataMap.keySet()) {
                com.fxiaoke.open.erpsyncdata.common.data.ObjectData detailData = destDetailSyncDataIdAndDestDataMap.get(syncDataId);
                //复制，不会复制大数据字段
                SyncDataContextEvent detailDoWriteMqData = doWriteResultData.deepCopyIgnoreBigData();
                detailDoWriteMqData.setSyncDataId(syncDataId);
                detailDoWriteMqData.setDestData(detailData);
                detailDoWriteMqData.setDestObjectApiName(detailData.getApiName());
                detailDoWriteMqData.setDestDataId(detailData.getId());
                //detailDoWriteMqData.setDestMasterDataId(mainDoWriteMqData.getDestData().getId());
                detailDoWriteMqData.setMasterMappingsData(doWriteResultData.getMasterMappingsData());
                String detailSyncDataEntityStr = JacksonUtil.toJson(syncDataMap.get(syncDataId));
                detailDoWriteMqData.setSyncDataEntityStr(detailSyncDataEntityStr);
                detailDoWriteMqData.setSyncLogBaseInfo(LogIdUtil.getBaseLogNoCreate());
                batchDoWriteData.getDetailContextList().add(detailDoWriteMqData);
            }
        }
        batchWriteCRMManager.putData(batchDoWriteData);
        return doWriteResultData.stop(i18NStringManager.getByEi(I18NStringEnum.s950, doWriteResultData.getTenantId()));
    }

    private SyncDataContextEvent stopBeforeWrite(String tenantId, SyncDataContextEvent syncDataContextEvent) {
        log.info("outer done themself and do not go this way, doWriteData={}", syncDataContextEvent);
        syncDataManager.fillSimpleSyncData(syncDataContextEvent.getSyncDataMap(), syncDataContextEvent);
        return syncDataContextEvent.next();
    }

    private SyncDataContextEvent beforeWrite(SyncDataContextEvent doWriteResultData) {
        try {
            write2CrmSpeedLimit(doWriteResultData);//限速统计
            SaveInterfaceMonitorAspect.setContextEvent(doWriteResultData);

            //前处理
            if (ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName().equals(doWriteResultData.getDestObjectApiName())) {
                doWriteResultData = skuSpuService.handleSkuSpu2Crm(doWriteResultData);//处理商品产品
            } else if (ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName().equals(doWriteResultData.getDestObjectApiName())) {
                doWriteResultData = syncCpqService.addBomPathField(doWriteResultData);//处理BOMObj,添加bom_path字段
            } else if (ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(doWriteResultData.getDestObjectApiName()) ||
                    ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(doWriteResultData.getDestObjectApiName())) {
                syncOrgService.beforWriteOrg2Crm(doWriteResultData);//处理人员,部门
            } else if (ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName().equals(doWriteResultData.getDestObjectApiName())) {
                syncMultipleUnitService.handleMultipleUnit2Crm(doWriteResultData);//处理多单位
            } else if (ObjectApiNameEnum.FS_BATCHSTOCK.getObjApiName().equals(doWriteResultData.getDestObjectApiName())) {
                doWriteResultData = syncStockService.beforeWriteBatchStock2Crm(doWriteResultData);//处理批次库存
            } else if (ObjectApiNameEnum.FS_SERIAL_NUMBER_OBJ.getObjApiName().equals(doWriteResultData.getDestObjectApiName())) {
                log.info("ErpProcessMessageServiceImpl.before,message={}", doWriteResultData);
                syncSerialNumberService.beforeWrite2Crm(doWriteResultData);//处理序列号
            } else if (ObjectApiNameEnum.FS_BOM_CORE_OBJ.match(doWriteResultData.getDestObjectApiName())) {
                syncBomCoreObjManager.beforeWrite(doWriteResultData);
            }
        } catch (Exception e) {
            log.warn("outer done their before method is exception,", e);
        } finally {
            SaveInterfaceMonitorAspect.cleanContextEvent();
        }
        return doWriteResultData;
    }

    private void afterWrite(SyncDataContextEvent doWriteResultDataResult) {
        try {//后处理
            if (doWriteResultDataResult.getDestEventType() == EventTypeEnum.ADD.getType()) {
                buriedSitesStatisticsUtils.uploadBuriedStitesLogBySnap(doWriteResultDataResult.getDestTenantId(),
                        doWriteResultDataResult.getDestObjectApiName(), 2,
                        doWriteResultDataResult.getSyncPloyDetailSnapshotId(), doWriteResultDataResult.getDataReceiveType());
            }
            if (ObjectApiNameEnum.FS_BATCHSTOCK.getObjApiName().equals(doWriteResultDataResult.getDestObjectApiName())) {
                //批次库存特殊处理
                syncStockService.afterWriteBatchStock2Crm(doWriteResultDataResult);
            } else if (ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(doWriteResultDataResult.getDestObjectApiName()) ||
                    ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(doWriteResultDataResult.getDestObjectApiName())) {
                //部门特殊处理（同步后）,人员特殊处理（同步后）
                syncDepartmentOrPersonnelService.afterWriteDepartmentOrPersonnel2Crm(doWriteResultDataResult);
            }
        } catch (Exception e) {
            log.warn("outer done their after method is exception, e" + e);
        }
    }

    private void doCreateCRM(String tenantId, HeaderObj headerObj, String crmObjectApiName, String syncDataId,
                             SyncPloyDetailData2 syncPloyDetailData, SyncDataContextEvent syncDataContextEvent) {
        Map<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailMaps = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
        //透传拿回对象apiName目标数据id与对应的syncDataId
        Map<String, Map<String, String>> apiNameAndDestDataIdSyncDataIdMap = new HashMap<>();
        Map<String, List<ObjectData>> detailDatasMap = new HashMap<>();
        if (detailMaps != null) {
            for (Entry<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailDataEntry : detailMaps.entrySet()) {
                com.fxiaoke.open.erpsyncdata.common.data.ObjectData detailData = detailDataEntry.getValue();
                String detailObjectApiName = detailData.getApiName();
                Map<String, String> destDataIdSyncDataIdMap = apiNameAndDestDataIdSyncDataIdMap.computeIfAbsent(detailObjectApiName, k -> new HashMap<>());
                destDataIdSyncDataIdMap.put(detailDataEntry.getValue().getId(), detailDataEntry.getKey());
                List<ObjectData> detailDataList = detailDatasMap.computeIfAbsent(detailObjectApiName, k -> new ArrayList<>());
                ObjectData fsObjectData = new ObjectData();
                fsObjectData.putAll(detailDataEntry.getValue());
                detailDataList.add(fsObjectData);
            }
        }
        ObjectData fsObjectData = new ObjectData();
        fsObjectData.putAll(syncDataContextEvent.getDestData());
        Result<ActionAddResult> result = this.createCrmObjectData(headerObj, fsObjectData, detailDatasMap, syncDataContextEvent);
        if (result.isSuccess()) {
            syncDataContextEvent.setDestDataNameAfterWrite(Opt.ofTry((() -> result.getData().getObjectData().getName())).get());
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            String id = result.getData().getObjectData().getId();
            writeResult.setSyncDataId(syncDataId);
            writeResult.setDestDataId(id);
            Map<String, List<ObjectData>> detailDataResultsMap = result.getData().getDetails();
            if (result.getData() != null) {
                needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(tenantId, syncDataId, result.getData().getObjectData(), syncPloyDetailData);
            }
            if (detailDataResultsMap != null) {
                needReturnData2SyncDataManager.doDetailNeedReturnData2SyncData(tenantId, detailDataResultsMap, apiNameAndDestDataIdSyncDataIdMap, syncPloyDetailData);
                for (Entry<String, List<ObjectData>> detailEntry : detailDataResultsMap.entrySet()) {
                    String detailObjectApiName = detailEntry.getKey();
                    List<ObjectData> detailObjectResults = detailEntry.getValue();
                    Map<String, String> destDataIdSyncDataIdMap = apiNameAndDestDataIdSyncDataIdMap.get(detailObjectApiName);
                    //明细处理
                    if (detailIdResultProcessReturnNeedBreak(syncDataContextEvent, detailObjectResults, destDataIdSyncDataIdMap, syncDataId, detailMaps, detailObjectApiName, detailWriteResults))
                        break;
                }
            }
            writeResult.setDestDetailSyncDataIdAndDestDataMap(syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap());
            syncDataContextEvent.setWriteResult(writeResult);
            syncDataContextEvent.setDetailWriteResults(detailWriteResults);
        } else {
//                创建失败,检查是否已有该对象和从对象
            processCreateObjectFail(Lists.newArrayList(syncDataContextEvent), result.getCode(), result.getMessage(),false);
        }
    }

    private void doUpdateCRM(String tenantId, HeaderObj headerObj, String destObjectApiName, String syncDataId,
                             SyncPloyDetailData2 syncPloyDetailData, SyncDataContextEvent syncDataContextEvent) {
        ObjectData objectData = new ObjectData();
        objectData.putAll(syncDataContextEvent.getDestData());
        Map<String, List<ObjectData>> detailDatasMap = null;
        Map<String, Map<String, String>> apiNameAndDestDataIdSyncDataIdMap = new HashMap<>();
        Map<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailMaps = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
        if (configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(destObjectApiName)) {
            /**crm销售订单和销售发票，要求主从一起用Edit全量更新，所以要把从数据的格式转换一下。
             * detailMaps中的value是所有从对象的数据混合放到一起，改为 detailDatasMap的把从对象apiname作为key。
             **/
            //透传拿回对象apiName目标数据id与对应的syncDataId
            detailDatasMap = new HashedMap();
            if (detailMaps != null) {
                for (Entry<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailDataEntry : detailMaps.entrySet()) {
                    com.fxiaoke.open.erpsyncdata.common.data.ObjectData detailData = detailDataEntry.getValue();
                    String detailObjectApiName = detailData.getApiName();
                    Map<String, String> destDataIdSyncDataIdMap = apiNameAndDestDataIdSyncDataIdMap.get(detailObjectApiName);
                    if (destDataIdSyncDataIdMap == null) {
                        destDataIdSyncDataIdMap = new HashMap<>();
                        apiNameAndDestDataIdSyncDataIdMap.put(detailObjectApiName, destDataIdSyncDataIdMap);
                    }
                    destDataIdSyncDataIdMap.put(detailDataEntry.getValue().getId(), detailDataEntry.getKey());
                    List<ObjectData> detailDataList = detailDatasMap.get(detailObjectApiName);
                    if (detailDataList == null) {
                        detailDataList = new ArrayList<>();
                        detailDatasMap.put(detailObjectApiName, detailDataList);
                    }
                    ObjectData fsObjectData = new ObjectData();
                    fsObjectData.putAll(detailDataEntry.getValue());
                    detailDataList.add(fsObjectData);
                }

            }
        }
        Result<ActionEditResult> result = updateCrmObjectData(tenantId, headerObj, destObjectApiName, objectData, detailDatasMap, syncDataContextEvent);
        if (configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(destObjectApiName)) {
            //CRM订单, 销售发票等对象要求主从一起更新
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            if (result.isSuccess()) {
                String id = result.getData().getObjectData().getId();
                syncDataContextEvent.setDestDataNameAfterWrite(Opt.ofTry((() -> result.getData().getObjectData().getName())).get());
                writeResult.setSyncDataId(syncDataId);
                writeResult.setDestDataId(id);
                if (result.getData() != null) {
                    needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(tenantId, syncDataId, result.getData().getObjectData(), syncPloyDetailData);
                }
                FindDetailDataListResult findDetailDataListResult = objectDataService.findDetailDataList(headerObj, destObjectApiName, objectData.getId()).getData();
                Map<String, List<ObjectData>> detailDataResultsMap = findDetailDataListResult.getDetailData();
                if (detailDataResultsMap != null) {
                    needReturnData2SyncDataManager.doDetailNeedReturnData2SyncData(tenantId, detailDataResultsMap, apiNameAndDestDataIdSyncDataIdMap, syncPloyDetailData);
                    for (Entry<String, List<ObjectData>> detailEntry : detailDataResultsMap.entrySet()) {
                        String detailObjectApiName = detailEntry.getKey();
                        List<ObjectData> detailObjectResults = detailEntry.getValue();
                        if (!apiNameAndDestDataIdSyncDataIdMap.containsKey(detailObjectApiName)) {
                            continue;
                        }
                        Map<String, String> destDataIdSyncDataIdMap = apiNameAndDestDataIdSyncDataIdMap.get(detailObjectApiName);
                        //明细处理
                        if (detailIdResultProcessReturnNeedBreak(syncDataContextEvent, detailObjectResults, destDataIdSyncDataIdMap, syncDataId, detailMaps, detailObjectApiName, detailWriteResults))
                            break;
                    }
                }
            } else {
                syncDataContextEvent.newError(writeResult, detailWriteResults, syncDataId,
                        Optional.ofNullable(detailMaps).map(v -> v.keySet()).orElse(null),
                        CompleteDataWriteArg.OUT_ERROR_CODE, i18NStringManager.getByEi(I18NStringEnum.s951, tenantId) + result.getMessage());
            }
            writeResult.setDestDetailSyncDataIdAndDestDataMap(syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap());
            syncDataContextEvent.setWriteResult(writeResult);
            syncDataContextEvent.setDetailWriteResults(detailWriteResults);
        } else if (result.isSuccess()) {
            if (result.getData() != null) {
                needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(tenantId, syncDataId, result.getData().getObjectData(), syncPloyDetailData);
            }
            syncDataContextEvent.setDestDataNameAfterWrite(Opt.ofTry((() -> result.getData().getObjectData().getName())).get());
            syncDataContextEvent.newSuccess(syncDataContextEvent.getDestEventType(), syncDataId, syncDataContextEvent.getDestDataId());
        } else {
            syncDataContextEvent.newError(syncDataContextEvent.getDestEventType(), syncDataId, i18NStringManager.getByEi(I18NStringEnum.s951, tenantId) + result.getMessage());
        }
    }


    private void tryDecreaseWriteCrmSpeed(String tenantId, String crmObjectApiName, boolean invokeCrmSucc) {
    }

    private void doInvalidOrDeleteCRM(String tenantId, HeaderObj headerObj, String crmObjectApiName, String syncDataId,
                                      SyncPloyDetailData2 syncPloyDetailData, SyncDataContextEvent syncDataContextEvent) {
        Result result = null;
        try {
            SaveInterfaceMonitorAspect.setContextEvent(syncDataContextEvent);
            if (syncDataContextEvent.getDestEventType() == EventTypeEnum.INVALID.getType()) {
                ActionBulkInvalidArg actionBulkInvalidArg = new ActionBulkInvalidArg(crmObjectApiName, Lists.newArrayList(syncDataContextEvent.getDestDataId()));
                result = metadataActionService.bulkInvalid(headerObj, crmObjectApiName, null, null, actionBulkInvalidArg);
                if (result.getCode() == 201112008) {//数据已作废
                    result.setCode(Result.SUCCESS_CODE);
                }
            } else if (syncDataContextEvent.getDestEventType() == EventTypeEnum.DELETE_DIRECT.getType()) {//这里目前没有用到，erp->crm方向还没有删除事件
                ObjectDataGetByIdResult objectDataGetByIdResult = objectDataService.getById(headerObj, crmObjectApiName, syncDataContextEvent.getDestDataId(), false, false, false, false).getData();
                if (objectDataGetByIdResult != null && objectDataGetByIdResult.getObjectData() != null) {
                    if(!CommonConstant.LIFE_STATUS_INVALID.equals(objectDataGetByIdResult.getObjectData().getLifeStatus())){
                        ActionBulkInvalidArg actionBulkInvalidArg = new ActionBulkInvalidArg(crmObjectApiName, Lists.newArrayList(syncDataContextEvent.getDestDataId()));
                        result = metadataActionService.bulkInvalid(headerObj, crmObjectApiName, null, null, actionBulkInvalidArg);
                    }
                    BulkDeleteArg actionBulkInvalidArg = new BulkDeleteArg();
                    actionBulkInvalidArg.setDataIds(Lists.newArrayList(syncDataContextEvent.getDestDataId()));
                    result = objectDataService.bulkDelete(headerObj, crmObjectApiName, actionBulkInvalidArg, true);
                } else {
                    result = new Result();
                    result.setCode(Result.SUCCESS_CODE);
                    result.setMessage("data is delete");
                }
            }
            tryDecreaseWriteCrmSpeed(tenantId, crmObjectApiName, true);
        } catch (CrmBusinessException e) {
            result = new Result<>();
            result.setCode(5000000);
            result.setMessage(e.getMessage());
            log.warn(e.getMessage(), e);
        } catch (Exception e) {
            if (e instanceof UndeclaredThrowableException) {
                if (e.getCause() instanceof InvocationTargetException) {
                    //http的code不是200， 或者timeout等都这里都会收到crm-rest-api抛出来的 InvocationTargetException。
                    log.warn("trace write create crm exception, ", e);
                    tryDecreaseWriteCrmSpeed(syncDataContextEvent.getTenantId(), crmObjectApiName, false);
                    speedLimitManager.countAndCheck(syncDataContextEvent.getTenantId(), SpeedLimitTypeEnum.TO_CRM, 1L, true);
                    //对CRM调用限速降低上限。 或者认为消耗百分之十的额度。
                    
                }
            }
            result = new Result<>();
            result.setCode(5000000);
            //将caused by 抛出
            //由于展示有限，只打印最底层异常
            Throwable rootCause = ExceptionUtil.getRootCause(e);
            String stackTrace = null;
            if (rootCause != null) {
                //将caused by 抛出
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                rootCause.printStackTrace(pw);
                stackTrace = sw.toString();
            }
            result.setMessage(i18NStringManager.getByEi(I18NStringEnum.s952, tenantId) + ",exception " +
                    Optional.ofNullable(stackTrace).map((s) -> s.length() > 500 ? s.substring(0, 500) : s).orElse(" null exception"));
            log.warn(e.getMessage(), e);
        } finally{
            SaveInterfaceMonitorAspect.cleanContextEvent();
        }
        if (result.isSuccess()) {
            syncDataContextEvent.newSuccess(syncDataContextEvent.getDestEventType(), syncDataId, syncDataContextEvent.getDestDataId());
        } else {
            if (result.getMessage() != null && result.getMessage().contains(i18NStringManager.getByEi(I18NStringEnum.s953, tenantId))) {//已经作废或者删除了
                syncDataContextEvent.newSuccess(syncDataContextEvent.getDestEventType(), syncDataId, syncDataContextEvent.getDestDataId());
            } else {
                syncDataContextEvent.newError(syncDataContextEvent.getDestEventType(), syncDataId, i18NStringManager.getByEi(I18NStringEnum.s951, tenantId) + result.getMessage());
            }

        }
    }



    public SyncDataContextEvent oneDataWrite(SyncDataContextEvent syncDataContextEvent) {
        log.info("start DoWriteMqConsumer.doWrite,ei:{},crmobj:{},syncdataid:{}", syncDataContextEvent.getTenantId(), syncDataContextEvent.getDestObjectApiName(), syncDataContextEvent.getSyncDataId());
        String syncDataId = syncDataContextEvent.getSyncDataId();
        try {
            final String tenantId = syncDataContextEvent.getDestTenantId();
            Integer destTenantId = Integer.valueOf(tenantId);
            String  destEa = "";
            try{
                destEa = eieaConverter.enterpriseIdToAccount(destTenantId);
                TraceContext.get().setEi(tenantId);
                TraceContext.get().setEa(destEa);
            }catch (Exception e){};
            HeaderObj headerObj = new HeaderObj(destTenantId, CrmConstants.SYSTEM_USER);
            headerObj.put("x-fs-peer-name", fsPeerName);
            String crmObjectApiName = syncDataContextEvent.getDestObjectApiName();

            final SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, syncDataContextEvent.getSyncPloyDetailSnapshotId()).getData().getSyncPloyDetailData();
            if (syncDataContextEvent.getDestEventType() == EventTypeEnum.ADD.getType()) {
                doCreateCRM(tenantId, headerObj, crmObjectApiName, syncDataId, syncPloyDetailData, syncDataContextEvent);
            } else if ((syncDataContextEvent.getDestEventType() == EventTypeEnum.INVALID.getType()
                    || syncDataContextEvent.getDestEventType() == EventTypeEnum.DELETE_DIRECT.getType())) {
                doInvalidOrDeleteCRM(tenantId, headerObj, crmObjectApiName, syncDataId, syncPloyDetailData, syncDataContextEvent);
            } else if (syncDataContextEvent.getDestEventType() == EventTypeEnum.UPDATE.getType()) {
                doUpdateCRM(tenantId, headerObj, crmObjectApiName, syncDataId, syncPloyDetailData, syncDataContextEvent);
            } else {
                syncDataContextEvent.newError(syncDataContextEvent.getDestEventType(), syncDataId, CompleteDataWriteArg.UNSUPPORT_EVENT_TYPE, "");
            }
        } catch (Exception e) {
            //具体的异常应该在上面的每个方法里面catch了， 正常情况下这里不能catch到异常, 只是做个兜底。
            //doCreateCRM会有因为参数的传递格式导致的报错
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            Map<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailMaps = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            syncDataContextEvent.newError(writeResult, detailWriteResults, syncDataId,
                    Optional.ofNullable(detailMaps).map(v -> v.keySet()).orElse(null),
                    CompleteDataWriteArg.OUT_ERROR_CODE, i18NStringManager.getByEi(I18NStringEnum.s951, syncDataContextEvent.getTenantId()) + e.getMessage());
            log.warn("trace doWrite get exception, ", e);
        }
        log.info("end DoWriteMqConsumer.doWrite,ei:{},crmobj:{},syncdataid:{}", syncDataContextEvent.getTenantId(), syncDataContextEvent.getDestObjectApiName(), syncDataContextEvent.getSyncDataId());
        return syncDataContextEvent;
    }

    private void processCreateObjectFail(SyncDataContextEvent syncDataContextEvent, int errorCode, String errorMessage,boolean isBatchWrite) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(syncDataContextEvent.getTenantId()), CrmConstants.SYSTEM_USER);
        String syncDataId = syncDataContextEvent.getSyncDataId();
        String destObjectApiName = syncDataContextEvent.getDestObjectApiName();
        Map<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailMaps = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
        SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
        List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();

        ObjectDataGetByIdResult objectDataGetByIdResult = null;
        Boolean isMasterExist = false;
        Boolean isAllDetailExist = true;
        List<SyncDataContextEvent.WriteResult> detailResultList = Lists.newArrayList();
        try {
            ObjectDataGetByIdResult detailObjectDataGetByIdResult = null;
            objectDataGetByIdResult = objectDataService.getById(headerObj, destObjectApiName, syncDataContextEvent.getDestDataId(), false, false, false, false).getData();
            if (objectDataGetByIdResult != null && objectDataGetByIdResult.getObjectData() != null) {
                isMasterExist = true;
            }
            if (isMasterExist && detailMaps != null) {
                for (String detailSyncDataId : detailMaps.keySet()) {
                    com.fxiaoke.open.erpsyncdata.common.data.ObjectData detailObjectData = detailMaps.get(detailSyncDataId);
                    detailObjectDataGetByIdResult = objectDataService.getById(headerObj, detailObjectData.getApiName(), detailObjectData.getId(), false, false, false, false).getData();
                    if (detailObjectDataGetByIdResult == null || detailObjectDataGetByIdResult.getObjectData() == null) {
                        isAllDetailExist = false;
                        break;
                    } else {
                        SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                        String id = detailObjectDataGetByIdResult.getObjectData().getId();
                        detailWriteResult.setSyncDataId(detailSyncDataId);
                        detailWriteResult.setDestDataId(id);
                        detailResultList.add(detailWriteResult);
                    }
                }
            }
        } catch (Exception e) {
            isMasterExist = false;
            isAllDetailExist = false;
            log.warn("获取crm对象数据异常", e);
        }
        if (isMasterExist && isAllDetailExist) {
            String id = objectDataGetByIdResult.getObjectData().getId();
            writeResult.setSyncDataId(syncDataId);
            writeResult.setDestDataId(id);
            detailWriteResults.addAll(detailResultList);//明细结果
            log.warn("创建数据失败，因为数据已存在，objectDataGetByIdResult=" + objectDataGetByIdResult);
        } else {
            syncDataContextEvent.newError(writeResult, detailWriteResults, syncDataId,
                    Optional.ofNullable(detailMaps).map(v -> v.keySet()).orElse(null),
                    CompleteDataWriteArg.OUT_ERROR_CODE, i18NStringManager.getByEi(I18NStringEnum.s951, syncDataContextEvent.getTenantId()) + errorMessage);
        }

        writeResult.setDestDetailSyncDataIdAndDestDataMap(syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap());
        syncDataContextEvent.setWriteResult(writeResult);
        syncDataContextEvent.setDetailWriteResults(detailWriteResults);
    }

    public void processCreateObjectFail(List<SyncDataContextEvent> dataContextEvents, int errorCode,
                                        String errorMessage,boolean isBatchWrite) {
        SyncDataContextEvent firstContextEvent = dataContextEvents.get(0);
        String tenantId = firstContextEvent.getTenantId();
        Integer eventType = firstContextEvent.getDestEventType();
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(firstContextEvent.getTenantId()), CrmConstants.SYSTEM_USER);
        String destObjectApiName = firstContextEvent.getDestObjectApiName();
        List<String> destDataLists = dataContextEvents.stream().filter(entity ->ObjectUtils.isNotEmpty(entity.getDestData())).map(entity ->entity.getDestData().getId()).collect(Collectors.toList());
        Result<ObjectDataQueryListByIdsResult> objectDataQueryListByIdsResultResult = objectDataService.queryListByIds(headerObj, destObjectApiName, destDataLists);
        //对比数据id
        List<String> crmExistDataIds = objectDataQueryListByIdsResultResult.getData().getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        //compare不存在的数据
        List<SyncDataContextEvent> createEvents = Lists.newArrayList();
        for (SyncDataContextEvent dataContextEvent : dataContextEvents) {
            String syncDataId = dataContextEvent.getSyncDataId();
            String destDataId=dataContextEvent.getDestData().getId();
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            //判断从对象 detailSyncdataId:objectData
            Map<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailMaps = dataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
            //crm已经存在的数据
            if (crmExistDataIds.contains(destDataId)) {
                boolean detailMapsError = false;
                if (detailMaps != null) {
                    //apiname :listObjectData
                    Map<String, List<com.fxiaoke.open.erpsyncdata.common.data.ObjectData>> detailObjByApiName = detailMaps.values().stream().collect(Collectors.groupingBy(com.fxiaoke.open.erpsyncdata.common.data.ObjectData::getApiName, Collectors.toList()));
                    // objDataId:detailSyncDataId
                    Map<String, String> detailSyncDataIdMap = detailMaps.entrySet().stream().collect(Collectors.toMap(entry -> entry.getValue().getId(), Entry::getKey));
                    for (String detailObjApiName : detailObjByApiName.keySet()) {
                        // 从对象太多的分组查询
//                        List<SyncDataContextEvent.WriteResult> detailResultList = Lists.newArrayList();
                        List<com.fxiaoke.open.erpsyncdata.common.data.ObjectData> objectData = detailObjByApiName.get(detailObjApiName);
                        List<String> objDataIds = objectData.stream().map(com.fxiaoke.open.erpsyncdata.common.data.ObjectData::getId).collect(Collectors.toList());
                        List<List<String>> detailObjIds = CollUtil.split(objDataIds, 1000);//从对象按照1000拆分
                        for (List<String> detailObjId : detailObjIds) {
                            Result<ObjectDataQueryListByIdsResult> detailObjectDataGetByIdResult = objectDataService.queryListByIds(headerObj, detailObjApiName, detailObjId);
                            if (detailObjectDataGetByIdResult.isSuccess()||detailObjectDataGetByIdResult.getData().getDataList().size() == detailObjId.size()) {
                                for (ObjectData data : detailObjectDataGetByIdResult.getData().getDataList()) {
                                    SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                                    String id = data.getId();
                                    String detailSyncDataId = detailSyncDataIdMap.get(id);
                                    detailWriteResult.setSyncDataId(detailSyncDataId);
                                    detailWriteResult.setDestDataId(id);
                                    detailWriteResults.add(detailWriteResult);
                                }
                            }else{
                                detailMapsError = true;
                                break;
                            }
                        }

                    }
                }
                if (detailMapsError) {
                    dataContextEvent.newError(writeResult, detailWriteResults, syncDataId,
                            Optional.ofNullable(detailMaps).map(v -> v.keySet()).orElse(null),
                            CompleteDataWriteArg.OUT_ERROR_CODE,
                            i18NStringManager.getByEi(I18NStringEnum.s951, dataContextEvent.getTenantId()) + errorMessage);
                }else{
                    String id = dataContextEvent.getDestDataId();
                    writeResult.setSyncDataId(syncDataId);
                    writeResult.setDestDataId(id);
                }
                writeResult.setDestDetailSyncDataIdAndDestDataMap(dataContextEvent.getDestDetailSyncDataIdAndDestDataMap());
                dataContextEvent.setWriteResult(writeResult);
                dataContextEvent.setDetailWriteResults(detailWriteResults);
            } else {
                if (errorCode == 301129005&&CollectionUtils.isNotEmpty(crmExistDataIds)) {
                    //crmExistDataIds 有时候可能因为配置的问题，指定了crm的_id,导致crm创建的_id不是syncdataEvent的destDataId。这里会引起循环。
                    //重试writecrm
                    createEvents.add(dataContextEvent);
                }
                dataContextEvent.newError(writeResult, detailWriteResults, syncDataId,
                        Optional.ofNullable(detailMaps).map(v -> v.keySet()).orElse(null),
                        CompleteDataWriteArg.OUT_ERROR_CODE, i18NStringManager.getByEi(I18NStringEnum.s951, dataContextEvent.getTenantId()) + errorMessage);
            }
        }
        if (CollectionUtils.isNotEmpty(createEvents) && isBatchWrite) {
            batchDoWriteAndAfter(tenantId, destObjectApiName, eventType, createEvents);
        }
    }

    /**
     * 明细结果处理，如果有异常的。直接中断
     *
     * @return 是否需要中断
     */
    private boolean detailIdResultProcessReturnNeedBreak(SyncDataContextEvent syncDataContextEvent,
                                                         List<ObjectData> detailObjectResults,
                                                         Map<String, String> destDataIdSyncDataIdMap,
                                                         String syncDataId,
                                                         Map<String, com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailMaps,
                                                         String detailObjectApiName,
                                                         List<SyncDataContextEvent.WriteResult> detailWriteResults) {
        //成功的明细id列表
        List<String> detailResultIds = detailObjectResults.stream().map(v -> v.getId()).collect(Collectors.toList());
        //参数传了，但是结果没有的明细Id列表。如果不为空，直接报错。
        Collection<String> argButNoResultDetailIds = CollUtil.subtract(destDataIdSyncDataIdMap.keySet(), detailResultIds);
        if (!argButNoResultDetailIds.isEmpty()) {
            syncDataContextEvent.newError(new SyncDataContextEvent.WriteResult(), detailWriteResults, syncDataId,
                    Optional.ofNullable(detailMaps).map(v -> v.keySet()).orElse(null),
                    CompleteDataWriteArg.SUCCESS_BUT_RESULT_ERROR, i18NStringManager.getByEi2(I18NStringEnum.s954.getI18nKey(),
                            syncDataContextEvent.getTenantId(),
                            String.format(I18NStringEnum.s954.getI18nValue(), detailObjectApiName, argButNoResultDetailIds),
                            Lists.newArrayList(detailObjectApiName, argButNoResultDetailIds.toString())));
            return true;
        }
        //都成功，直接使用参数返回结果
        destDataIdSyncDataIdMap.forEach((dDataId, dSyncDataId) -> {
            SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
            detailWriteResult.setDestDataId(dDataId);
            //根据Id匹配。
            detailWriteResult.setSyncDataId(dSyncDataId);
            detailWriteResults.add(detailWriteResult);
        });
        return false;
    }

    private boolean canFillOutOwner(ObjectData objectData, HeaderObj headerObj, SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        int userId = headerObj.getInt("x-fs-userInfo");
        boolean support = sfaApiManager.isEnterpriseRelationSupport(tenantId,
                userId);
        if (support) {
            final String syncPloyDetailSnapshotId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
            return haveOutDataPrivilegeField(tenantId, userId, syncDataContextEvent.getDestObjectApiName(), syncPloyDetailSnapshotId, objectData);
        }
        return false;
    }

    public static final Set<String> DeliveryNoteApiName = Sets.newHashSet("DeliveryNoteProductObj", "DeliveryNoteObj");

    /**
     * 只有关联客户/合作伙伴对象的字段才需要补充外部负责人
     */
    public boolean haveOutDataPrivilegeField(final String tenantId, final int userId, final String destObjectApiName, final String syncPloyDetailSnapshotId, final ObjectData objectData) {
        // 获取字段映射
        final SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, syncPloyDetailSnapshotId).getData().getSyncPloyDetailData();
        List<FieldMappingData> fieldMappings;
        if (syncPloyDetailData.getDestObjectApiName().equals(destObjectApiName)) {
            fieldMappings = syncPloyDetailData.getFieldMappings();
        } else {
            fieldMappings = syncPloyDetailData.getDetailObjectMappings().stream()
                    .filter(detailObjectMappingData -> detailObjectMappingData.getDestObjectApiName().equals(destObjectApiName))
                    .findFirst()
                    .map(DetailObjectMappingsData.DetailObjectMappingData::getFieldMappings)
                    .orElseGet(ArrayList::new);
        }

        return fieldMappings.stream()
                // 获取关联客户/合作伙伴对象的字段
                .filter(fieldMapping -> SyncObjectFieldUtil.isObjectReferenceFieldType(fieldMapping.getDestType()) && CrmObjectApiName.OutDataPrivilegeFields.contains(fieldMapping.getDestTargetApiName()))
                .map(FieldMappingData::getDestApiName)
                // 字段有变更
                .filter(objectData::containsKey)
                .anyMatch(fieldName -> BooleanUtils.isTrue(sfaApiManager.haveOutDataPrivilege(tenantId, userId,
                        destObjectApiName, fieldName)));
    }

    private Result<ActionEditResult> updateCrmObjectData(final String tenantId,
                                                         HeaderObj headerObj,
                                                         String crmObjectApiName,
                                                         ObjectData fsObjectData,
                                                         Map<String, List<ObjectData>> detailDatasMap,
                                                         SyncDataContextEvent doWriteMqData) {
        log.info("DoWriteMqConsumer.updateCrmObjectData,objectApiName={},fsObjectData={},detailDatasMap={},doWriteMqData={}",
                crmObjectApiName,
                JSONObject.toJSONString(fsObjectData),
                JSONObject.toJSONString(detailDatasMap),
                JSONObject.toJSONString(doWriteMqData));
        Result<ActionEditResult> result = null;
        try {
            SaveInterfaceMonitorAspect.setContextEvent(doWriteMqData);
            Set<String> notUsePaasUpdateApiName = tenantConfigurationManager.getNotUsePaasUpdateApiName(tenantId);
            if (userPaasUpdateApiNames.contains(crmObjectApiName) && !notUsePaasUpdateApiName.contains(crmObjectApiName)) {
                Result<ObjectData> updataResult = objectDataService.updateObjectData(headerObj, crmObjectApiName, fsObjectData.getId(), null, fsObjectData);
                //objectDataService.updateObjectData的objectData在往下一层
                ObjectData objectData = null;
                if (updataResult.isSuccess()) {
                    objectData = Opt.ofTry(() -> ObjectData.convert(updataResult.getData().getMap("object_data"))).get();
                }
                changeOwnerCheckFirst(updataResult, fsObjectData, headerObj, crmObjectApiName);
                result = new Result<>();
                ActionEditResult actionEditResult = new ActionEditResult();
                actionEditResult.setObjectData(objectData);
                result.setData(actionEditResult);
                result.setCode(updataResult.getCode());
                result.setMessage(updataResult.getMessage());
            } else {
                ActionEditArg actionEditArg = new ActionEditArg();
                actionEditArg.setObjectData(fsObjectData);
                actionEditArg.setDetails(new HashMap<>());
                //和赵琚确认，只有新增单据支持自动填充外部负责人，更新不支持，所以，这个代码先注释掉
//                boolean fillOutOwner = canFillOutOwner(fsObjectData,headerObj,doWriteMqData);
//                actionEditArg.setFillOutOwner(fillOutOwner);
                if (fsObjectData.getApiName().equals(CheckinsFieldConstants.API_NAME)) {
                    //没发现策略使用这个
                    result = metadataActionService.sysEdit(headerObj, crmObjectApiName, null, null, actionEditArg);
                    changeOwnerActionEdit(result, fsObjectData, headerObj, crmObjectApiName);
                } else {
                    if (detailDatasMap != null) {
                        actionEditArg.setDetails(detailDatasMap);
                    }
                    if (StringUtils.equalsIgnoreCase(crmObjectApiName, PriceBookConstant.PRICE_BOOK_OBJ)) {
                        setPriceBookObjDetail(tenantId, headerObj, actionEditArg);
                    }
                    final boolean syncDetailFillMasterUpdate = syncDetailFillMasterUpdateApiNames.contains(crmObjectApiName);
                    if (syncDetailFillMasterUpdate) {
                        /**crm的主从对象大多支持分开更新，但是有些对象在更新从对象时要补齐主对象apiname，比如回款明细。*/
                        //从对象拉取主对象同步
                        String masterObjectApiName = outerServiceFactory.get(TenantTypeEnum.CRM.getType()).getMasterObjectApiName(String.valueOf(actionEditArg.getObjectData().getTenantId()), TenantTypeEnum.CRM.getType(), actionEditArg.getObjectData().getApiName()).getData();
                        String destMasterDataId = doWriteMqData.getMasterMappingsData() != null ? doWriteMqData.getMasterMappingsData().getDestDataId() : null;
                        actionEditArg = fillMasterCoverOrAddDetailDatas(headerObj, actionEditArg, destMasterDataId, masterObjectApiName);
                        crmObjectApiName = masterObjectApiName;
                    }

                    if (syncDetailFillMasterUpdate || (!DeliveryNoteApiName.contains(fsObjectData.getApiName())) || configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(crmObjectApiName)) {
                        result = actionEdit(headerObj, crmObjectApiName, fsObjectData, actionEditArg);
                    } else {
                        /**crm的发货单和发货单产品 按照深研李秋林的要求，用incrementUpdateupdate, 否则跳过很多业务逻辑。*/
                        result = deliveryNoteIncrementUpdate(headerObj, crmObjectApiName, fsObjectData);
                    }
                }
            }

            tryDecreaseWriteCrmSpeed(tenantId, crmObjectApiName, true);
        } catch (CrmBusinessException e) {
            result = new Result<>();
            result.setCode(5000000);
            result.setMessage(e.getMessage());
            log.warn(e.getMessage(), e);
        } catch (Exception e) {
            if (e instanceof UndeclaredThrowableException) {
                if (e.getCause() instanceof InvocationTargetException) {
                    //http的code不是200， 或者timeout等都这里都会收到crm-rest-api抛出来的 InvocationTargetException。
                    log.warn("trace write create crm exception, ", e);
                    tryDecreaseWriteCrmSpeed(tenantId, crmObjectApiName, false);  //对CRM调用限速降低上限。 或者认为消耗百分之十的额度。     
                    speedLimitManager.countAndCheck(tenantId, SpeedLimitTypeEnum.TO_CRM, (long)detailDatasMap.size()+1, true);                 
                }
            }
            result = new Result<>();
            result.setCode(5000000);
            //由于展示有限，只打印最底层异常
            Throwable rootCause = ExceptionUtil.getRootCause(e);
            String stackTrace = null;
            if (rootCause != null) {
                //将caused by 抛出
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                rootCause.printStackTrace(pw);
                stackTrace = sw.toString();
            }
            result.setMessage(i18NStringManager.getByEi(I18NStringEnum.s952, tenantId) + ",exception " +
                    Optional.ofNullable(stackTrace).map((s) -> s.length() > 500 ? s.substring(0, 500) : s).orElse(" null exception"));
            log.warn(e.getMessage(), e);
        } finally {
            SaveInterfaceMonitorAspect.cleanContextEvent();
        }
        return result;
    }

    private void setPriceBookObjDetail(String tenantId, HeaderObj headerObj, ActionEditArg actionEditArg) {
        List<ObjectData> applyAccountList = queryApplyAccountByPriceBookObj(headerObj, actionEditArg.getObjectData().getId());
        if (CollectionUtils.isNotEmpty(applyAccountList)) {

            String apply_account_range = actionEditArg.getObjectData().getString("apply_account_range");
            log.info("DoWriteMqConsumer.updateCrmObjectData,apply_account_range={}", apply_account_range);
            if (StringUtils.isNotEmpty(apply_account_range)) {
                String type = null;
                try {
                    JSONObject jsonObject = JSONObject.parseObject(apply_account_range);
                    type = jsonObject.getString("type");
                } catch (Exception e) {

                }
                if (StringUtils.isNotEmpty(type) && StringUtils.equalsIgnoreCase(type, "FIXED")) {
                    for (int i = 0; i < applyAccountList.size(); i++) {
                        ObjectData objectData = applyAccountList.get(i);
                        String apply_range = objectData.getString("apply_range");
                        log.info("DoWriteMqConsumer.updateCrmObjectData,apply_range={}", apply_range);
                        if (StringUtils.equalsIgnoreCase(apply_range, "ALL")) {
                            SyncDataMappingsEntity mappingsEntity = syncDataMappingsManager.getByDestData2(tenantId,
                                    PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ,
                                    objectData.getId());
                            if (mappingsEntity == null) {
                                applyAccountList.remove(i);
                                log.info("DoWriteMqConsumer.updateCrmObjectData,remove apply_range=ALL,objectData={}", objectData);
                            }
                            break;
                        }
                    }
                }
            }
            Map<String, List<ObjectData>> detailMap = new HashMap<>();
            detailMap.put(PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ, applyAccountList);
            actionEditArg.setDetails(detailMap);
        }
    }

    private Result<ActionEditResult> actionEdit(final HeaderObj headerObj, final String objectApiName, final ObjectData fsObjectData, final ActionEditArg actionEditArg) {
        log.info("DoWriteMqConsumer.updateCrmObjectData,edit,objectApiName={},actionEditArg={}",
                objectApiName,
                JSONObject.toJSONString(actionEditArg, SerializerFeature.WriteMapNullValue));
        Boolean isSpecifyCreatedBy = false, isSpecifyTime = false;
        if (actionEditArg.getObjectData() != null) {
            isSpecifyCreatedBy = actionEditArg.getObjectData().containsKey("created_by");
            isSpecifyTime = actionEditArg.getObjectData().containsKey("create_time");
        }
        final Result<ActionEditResult> edit = metadataActionService.edit(headerObj, objectApiName, isSpecifyCreatedBy, isSpecifyTime, null, null, actionEditArg);
        changeOwnerActionEdit(edit, fsObjectData, headerObj, objectApiName);
        return edit;
    }

    /**
     * crm的发货单和发货单产品 按照深研李秋林的要求，用incrementUpdate, 否则跳过很多业务逻辑。
     * 只有非主从一起更新的时候走这里
     */
    private Result<ActionEditResult> deliveryNoteIncrementUpdate(final HeaderObj headerObj, final String objectApiName, final ObjectData fsObjectData) {
        IncrementUpdateArg incrementUpdateArg = new IncrementUpdateArg();
        incrementUpdateArg.setData(fsObjectData);
        Result<IncrementUpdateResult> incrementUpdateResultResult = metadataActionService.incrementUpdate(headerObj, objectApiName, null, incrementUpdateArg);
        changeOwnerCheckFirst(incrementUpdateResultResult, fsObjectData, headerObj, objectApiName);
        final Result<ActionEditResult> result = new Result<>();
        result.setCode(incrementUpdateResultResult.getCode());
        result.setMessage(incrementUpdateResultResult.getMessage());
        log.debug("trace incrementUpdate,apiname:{}, data:{}, result:{} ", objectApiName, incrementUpdateArg, result);
        return result;
    }

    private void changeOwnerActionEdit(Result<ActionEditResult> result, ObjectData fsObjectData, HeaderObj headerObj, String objectApiName) {
        try {
            if (result.isSuccess() && fsObjectData.getOwner() != null) {
                Integer newOwner = fsObjectData.getOwner();
                Integer currentOwner = result.getData().getObjectData().getOwner();
                changeOwner(newOwner, currentOwner, headerObj, objectApiName, fsObjectData);
            }
        } catch (Exception e) {
            log.error("changeOwnerActionEdit change owner exception", e);
        }
    }

    private void changeOwnerCheckFirst(Result<?> result, ObjectData fsObjectData, HeaderObj headerObj, String objectApiName) {
        try {
            //需要再查询一遍数据
            GetByIdArg getByIdArg = new GetByIdArg();
            getByIdArg.setDescribeApiName(objectApiName);
            getByIdArg.setDataId(fsObjectData.getId());
            getByIdArg.setSelectFields(Collections.singletonList("owner"));
            Result<ObjectDataGetByIdV3Result> byId = objectDataServiceV3.getById(headerObj, getByIdArg);
            if (byId.isSuccess() && byId.getData() != null && byId.getData().getObjectData() != null
                    && result.isSuccess() && fsObjectData.getOwner() != null) {
                Integer currentOwner = byId.getData().getObjectData().getOwner();
                Integer newOwner = fsObjectData.getOwner();
                changeOwner(newOwner, currentOwner, headerObj, objectApiName, fsObjectData);
            }
        } catch (Exception e) {
            log.error("changeOwnerIncrementUpdate change owner exception", e);
        }
    }

    private void changeOwner(Integer newOwner, Integer currentOwner, HeaderObj headerObj, String objectApiName, ObjectData fsObjectData) {
        boolean needChangeOwner = newOwner != null && !newOwner.equals(currentOwner);
        log.debug("change owner,newOwner:{},currentOwner:{},need change:{}", newOwner, currentOwner, needChangeOwner);
        if (needChangeOwner) {
            Result<ActionChangeOwnerResult> acoResult = metadataActionService.changeOwner(headerObj, objectApiName, null, null, new ActionChangeOwnerArg(fsObjectData.getId(), newOwner));
            if (!acoResult.isSuccess()) {
                if (!"320001401".equals(acoResult.getCode())) {//message: "不可单独更换从对象的负责人", 这个就不打印了。
                    log.warn("更新负责人失败,ei:{}, obj:{} code: {}, message: {}", fsObjectData.getTenantId(), objectApiName, acoResult.getCode(), acoResult.getMessage());
                }
            }
        }
    }

    private ActionEditArg fillMasterCoverOrAddDetailDatas(HeaderObj headerObj, ActionEditArg actionEditArg, String destMasterDataId, String masterObjectApiName) {
        ObjectData detatilDestData = actionEditArg.getObjectData();
        FindDetailDataListResult findDetailDataListResult = objectDataService.findDetailDataList(headerObj, masterObjectApiName, destMasterDataId).getData();
        Boolean hasDetail = false;
        for (Entry<String, List<ObjectData>> entry : findDetailDataListResult.getDetailData().entrySet()) {
            if (entry.getKey().equals(detatilDestData.getApiName())) {
                for (ObjectData detailObjectData : entry.getValue()) {
                    if (detailObjectData.getId().equals(detatilDestData.getId())) {
                        detailObjectData.putAll(detatilDestData);
                        hasDetail = true;
                        break;
                    }
                }
                if (!hasDetail) {
                    entry.getValue().add(detatilDestData);
                }
            }
        }

        //去掉主从对象的version字段信息，避免CRM报  您当前编辑的数据已被他人修改，请关闭页面后重试
        findDetailDataListResult.getMasterData().remove("version");
        for (Entry<String, List<ObjectData>> entry : findDetailDataListResult.getDetailData().entrySet()) {
            for (ObjectData objectData : entry.getValue()) {
                objectData.remove("version");
            }
        }

        ActionEditArg actionEditArgReturn = new ActionEditArg();
        actionEditArgReturn.setDetails(findDetailDataListResult.getDetailData());
        actionEditArgReturn.setObjectData(findDetailDataListResult.getMasterData());
        return actionEditArgReturn;
    }

//    /**
//     * 填充主对象的从对象数据
//     */
//    private ActionEditArg fillDetailDatas(HeaderObj headerObj, ActionEditArg actionEditArg) {
//        ObjectData masterData = actionEditArg.getObjectData();
//        String masterApiName = masterData.getApiName();
//        FindDetailDataListResult findDetailDataListResult = objectDataService.findDetailDataList(headerObj, masterApiName, masterData.getId()).getData();
//        ObjectData oldMasterData = findDetailDataListResult.getMasterData();
//        oldMasterData.putAll(masterData);
//        actionEditArg.setDetails(findDetailDataListResult.getDetailData());
//        return actionEditArg;
//    }

    /**
     * 查询价目表适用客户数据
     *
     * @param priceBookId 价目表id
     * @return
     */
    public List<ObjectData> queryApplyAccountByPriceBookObj(HeaderObj headerObj, String priceBookId) {
        GetRelatedDataListArg arg = new GetRelatedDataListArg();
        arg.setAssociateObjectDataId(priceBookId);
        arg.setAssociateObjectDescribeApiName(PriceBookConstant.PRICE_BOOK_OBJ);
        arg.setAssociatedObjectDescribeApiName(PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ);
        arg.setAssociatedObjectFieldRelatedListName("price_book_price_book_account_list");
        arg.setIncludeAssociated(true);
        arg.setOrdered(true);

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(2000);

        arg.setSearchQueryInfo(GsonUtil.toJson(searchQuery));

        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> result = metadataControllerService.getRelatedDataList(headerObj,
                PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ,
                arg);
        if (result.isSuccess()) {
            return result.getData().getDataList();
        }

        log.info("DoWrite2CrmManager.queryApplyAccountByPriceBookObj,result={}", result);
        return new ArrayList<>();
    }

    private Result<ActionAddResult> createCrmObjectData(HeaderObj headerObj, ObjectData fsObjectData, @NotNull Map<String, List<ObjectData>> detailDatasMap, SyncDataContextEvent syncDataContextEvent) {
        if (!detailDatasMap.isEmpty()) {
            Integer masterOwnerId = fsObjectData.getOwner();
            for (List<ObjectData> objectDataList : detailDatasMap.values()) {
                for (ObjectData objectData : objectDataList) {
                    Integer detailOwnerId = objectData.getOwner();
                    if (detailOwnerId == null) {
                        objectData.setOwner(masterOwnerId);
                    }
                }
            }
        }

        Result<ActionAddResult> result = null;
        String objectApiName = fsObjectData.getApiName();
        try {
            SaveInterfaceMonitorAspect.setContextEvent(syncDataContextEvent);
            Set<String> notUsePaasAddApiName = tenantConfigurationManager.getNotUsePaasAddApiName(syncDataContextEvent.getTenantId());
            if (userPaasAddApiNames.contains(objectApiName) && !notUsePaasAddApiName.contains(objectApiName)) {
                Result<ObjectDataCreateResult> fsObjectDataResult = objectDataService.create(headerObj,
                        objectApiName, false, true, false, fsObjectData);
                result = new Result<>();
                result.setCode(fsObjectDataResult.getCode());
                result.setMessage(fsObjectDataResult.getMessage());
                ActionAddResult actionAddResult = new ActionAddResult();
                result.setData(actionAddResult);
                actionAddResult.setObjectData(fsObjectDataResult.getData().getObjectData());
                Map<String, List<ObjectData>> details = Maps.newHashMap();
                result.getData().setDetails(details);
                if (Lists.newArrayList(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ, SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ).contains(objectApiName) && !detailDatasMap.values().isEmpty()) {
                    log.info("MultiUnitRelatedObj and SpuSkuSpecValueRelateObj have detailDatas and detailDatas={}", detailDatasMap.values());
                }
                for (List<ObjectData> objectDataList : detailDatasMap.values()) {
                    for (ObjectData objectData : objectDataList) {
                        Result<ObjectDataCreateResult> detailDataResult = objectDataService.create(headerObj, objectData.getApiName(), false, true, false, objectData);
                        if (detailDataResult.isSuccess()) {
                            details.putIfAbsent(detailDataResult.getData().getObjectData().getApiName(), Lists.newArrayList());
                            details.get(detailDataResult.getData().getObjectData().getApiName()).add(detailDataResult.getData().getObjectData());
                        } else {
                            log.warn("objectDataService#create is fail, detailDataResult={}, objectData={}", detailDataResult, objectData);
                        }
                    }
                }
                return result;
            } else {
                //配置的企业对象，支持跳过查重规则
                ActionAddArg addArg = new ActionAddArg();
                fsObjectData.putAll(fsObjectData);
                addArg.setObjectData(fsObjectData);
                boolean fillOutOwner = canFillOutOwner(fsObjectData, headerObj, syncDataContextEvent);
                addArg.setFillOutOwner(fillOutOwner);
                addArg.setDetails(new HashMap<>());
                String tenantId = String.valueOf(headerObj.get("X-fs-ei"));
                ErpTenantConfigurationEntity searchSetting = tenantConfigurationManager.findOne(tenantId, "0", "ALL", TenantConfigurationTypeEnum.SKIP_DUPLICATE_SEARCH.name());
                if (ObjectUtils.isNotEmpty(searchSetting)) {
                    List<String> apiNames = Splitter.on(";").splitToList(searchSetting.getConfiguration());
                    if (apiNames.contains(objectApiName)) {
                        ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
                        optionInfo.setIsDuplicateSearch(false);
                        addArg.setOptionInfo(optionInfo);
                    }
                }
                if (PriceBookConstant.PRICE_BOOK_OBJ.equals(fsObjectData.getApiName())) {
                    //如果价目表有适用客户从对象，适用客户从对象必须和价目表主对象一起新建

                    ErpTenantConfigurationEntity rangeEntity = tenantConfigurationManager.findOne(tenantId, "ALL", ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.RANG_EXPRESSION.name());
                    if (ObjectUtils.isNotEmpty(rangeEntity)) {
                        if (ObjectUtils.isNotEmpty(addArg.getObjectData().get("apply_account_range"))) {
                            if (addArg.getObjectData().get("apply_account_range").toString().contains("CONDITION")) {
                                //当apply_account_range是条件表达式的时候，需要删除从对象数据。
                                detailDatasMap.put(PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ, null);
                            }
                        }
                    }
                    List<ObjectData> accountDataList = detailDatasMap.get(PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ);
                    if (CollectionUtils.isNotEmpty(accountDataList)) {
                        Map<String, List<ObjectData>> accountDetailDatasMap = new HashMap<>();
                        accountDetailDatasMap.put(PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ, accountDataList);
                        addArg.setDetails(accountDetailDatasMap);
                    }
                    log.info("DoWrite2CrmManager.createCrmObjectData,assembly addArg,addArg={}", JSONObject.toJSONString(addArg));
                } else {
                    //价目表主从要分开新建。
                    addArg.setDetails(detailDatasMap);
                }

                if (fsObjectData.getApiName().equals(CheckinsFieldConstants.API_NAME)) {
                    result = metadataActionService.sysAdd(headerObj, fsObjectData.getApiName(), null, null, addArg);
                } else if (PriceBookConstant.PRICE_BOOK_PRODUCT_OBJ.equals(fsObjectData.getApiName())) {
                    //单独新增价目表明细
                    ActionBulkCreateArg bulkCreateArg = new ActionBulkCreateArg();
                    bulkCreateArg.setDataList(Lists.newArrayList(fsObjectData));
                    Result<ActionBulkCreateResult> detailResult = metadataActionService.bulkCreate(headerObj, fsObjectData.getApiName(), null, null, bulkCreateArg);
                    result = new Result<>();
                    if (detailResult.isSuccess()) {
                        ActionAddResult actionAddResult = new ActionAddResult();
                        actionAddResult.setObjectData(detailResult.getData().getDataList().get(0));
                        result.setData(actionAddResult);
                    } else {
                        log.warn("metadataActionService#bulkCreate single add fail, detailDatasMap={}, detailResult={}", detailDatasMap, detailResult);
                    }
                    result.setCode(detailResult.getCode());
                    result.setMessage(detailResult.getMessage());
                } else {
                    Boolean isSpecifyCreatedBy = false, isSpecifyTime = false;
                    if (addArg.getObjectData() != null) {
                        isSpecifyCreatedBy = addArg.getObjectData().containsKey("created_by");
                        isSpecifyTime = addArg.getObjectData().containsKey("create_time");
                    }
                    result = metadataActionService.add(headerObj, fsObjectData.getApiName(), isSpecifyCreatedBy, isSpecifyTime, null, null, addArg);
                    //如果是价目表则单独添加价目表明细
                    if (PriceBookConstant.PRICE_BOOK_OBJ.equals(fsObjectData.getApiName()) && result.isSuccess() && CollUtil.isNotEmpty(detailDatasMap)) {
                        if (result.getData().getDetails() == null) {
                            result.getData().setDetails(new HashMap<>());
                        }
                        for (Entry<String, List<ObjectData>> entry : detailDatasMap.entrySet()) {
                            String detailApiName = entry.getKey();
                            //价目表的适用客户对象已经和价目表对象一起新建了，这里就不需要再单独新建了
                            if (StringUtils.equalsIgnoreCase(detailApiName, PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ)) {
                                continue;
                            }
                            //将阻挡这个分支，以防万一，加个日志看下是否还有走到的
                            log.warn("unexpect branch for PRICE_BOOK_OBJ");
                            //分批写，每批180
                            List<List<ObjectData>> pageList = PageUtils.getPageList(entry.getValue(), 180);
                            for (int page = 0; page < pageList.size(); page++) {
                                ActionBulkCreateArg bulkCreateArg = new ActionBulkCreateArg();
                                bulkCreateArg.setDataList(pageList.get(page));
                                Result<ActionBulkCreateResult> detailResult = metadataActionService.bulkCreate(headerObj, detailApiName, null, null, bulkCreateArg);
                                if (detailResult.isSuccess()) {
                                    log.info("DoWrite2CrmManager.createCrmObjectData,bulkCreate,page={},detailResult.size={}",
                                            page, detailResult.getData().getDataList().size());
                                } else {
                                    log.info("DoWrite2CrmManager.createCrmObjectData,bulkCreate,page={}", page);
                                }
                                if (detailResult.isSuccess()) {
                                    if (CollectionUtils.isEmpty(result.getData().getDetails().get(detailApiName))) {
                                        result.getData().getDetails().put(detailApiName, detailResult.getData().getDataList());
                                    } else {
                                        result.getData().getDetails().get(detailApiName).addAll(detailResult.getData().getDataList());
                                        log.info("DoWrite2CrmManager.createCrmObjectData,bulkCreate,result.getData().getDetails().size={}",
                                                result.getData().getDetails().get(detailApiName).size());
                                    }
                                } else {
                                    //cre rest api的result，当不成功的时候，toString会失败，但是在log方法里toString是安全的所以不会抛异常
                                    log.warn("metadataActionService#bulkCreate fail, detailDatasMap={}, detailResult={}", detailDatasMap, detailResult.getCode() + detailResult.getMessage());
                                }
                            }
                            log.info("DoWrite2CrmManager.createCrmObjectData,detailApiName={} write success,size={}",
                                    detailApiName, result.getData().getDetails().get(detailApiName).size());
                        }
                        log.info("DoWrite2CrmManager.createCrmObjectData,PriceBookObj,all write success");
                    }
                }
            }
            tryDecreaseWriteCrmSpeed(syncDataContextEvent.getTenantId(), objectApiName, true);
        } catch (CrmBusinessException e) {
            result = new Result<>();
            result.setCode(5000000);
            result.setMessage(e.getMessage());
            log.warn(e.getMessage(), e);
        } catch (Exception e) {
            if (e instanceof UndeclaredThrowableException) {
                if (e.getCause() instanceof InvocationTargetException) {
                    //http的code不是200， 或者timeout等都这里都会收到crm-rest-api抛出来的 InvocationTargetException。
                    log.warn("trace write create crm exception, ", e);
                    tryDecreaseWriteCrmSpeed(syncDataContextEvent.getTenantId(), objectApiName, false);
                    speedLimitManager.countAndCheck(syncDataContextEvent.getTenantId(), SpeedLimitTypeEnum.TO_CRM, (long)detailDatasMap.size()+1, true);
                    //对CRM调用限速降低上限。 或者认为消耗百分之十的额度。
                }
            }
            result = new Result<>();
            result.setCode(5000000);
            //由于展示有限，只打印最底层异常
            Throwable rootCause = ExceptionUtil.getRootCause(e);
            String stackTrace = null;
            if (rootCause != null) {
                //将caused by 抛出
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                rootCause.printStackTrace(pw);
                stackTrace = sw.toString();
            }
            result.setMessage(i18NStringManager.getByEi(I18NStringEnum.s952, syncDataContextEvent.getTenantId()) + ",exception " +
                    Optional.ofNullable(stackTrace).map((s) -> s.length() > 500 ? s.substring(0, 500) : s).orElse(" null exception"));
            log.warn(e.getMessage(), e);
        } finally {
            SaveInterfaceMonitorAspect.cleanContextEvent();
        }
        return result;
    }

    public String getCrmObjectUrl(String tenantId, String objectApiName, String mainObjectApiName, ErpObjInterfaceUrlEnum urlEnum) {//mainObjectApiName有可能是主对象apiName,有可能不是
        if (urlEnum == null) {
            return objectApiName;
        }
        String interfaceUrl = "";
        Map<String, List<String>> batchWrite2Crm = configCenterConfig.getBatchWrite2CrmTenantAndObjApiName();
        boolean batchWrite = batchWrite2Crm.containsKey(tenantId)
                && CollectionUtils.isNotEmpty(batchWrite2Crm.get(tenantId))
                && batchWrite2Crm.get(tenantId).contains(objectApiName);
        if (batchWrite) {
            switch (urlEnum) {
                case create:
                    interfaceUrl = "/fs-metadata-rest/paas/metadata/data/bulk/create";
                    break;
                case update:
                    interfaceUrl = "/fs-metadata-rest/paas/metadata/data/batch/update/with/data";
                    break;
                case invalid:
                    interfaceUrl = "v1/rest/object/{apiName}/action/BulkInvalid".replace("{apiName}", objectApiName);
                    break;
                case delete:interfaceUrl="v1/inner/rest/object_data/{apiName}/bulk_delete".replace("{apiName}", objectApiName);
                    break;
                case recover:
                default:
                    interfaceUrl = objectApiName;
            }
        } else {
            switch (urlEnum) {
                case create:
                    Set<String> notUsePaasAddApiName = tenantConfigurationManager.getNotUsePaasAddApiName(tenantId);
                    if (userPaasAddApiNames.contains(objectApiName) && !notUsePaasAddApiName.contains(objectApiName)) {
                        interfaceUrl = "v1/inner/rest/object_data/{describeAPIName}".replace("{describeAPIName}", objectApiName);
                    } else {
                        if (CheckinsFieldConstants.API_NAME.equals(objectApiName)) {
                            interfaceUrl = "v1/rest/object/{apiName}/action/SysAdd".replace("{apiName}", objectApiName);
                        } else if (PriceBookConstant.PRICE_BOOK_PRODUCT_OBJ.equals(objectApiName)) {
                            interfaceUrl = "v1/rest/object/{apiName}/action/BulkCreate".replace("{apiName}", objectApiName);
                        } else {
                            interfaceUrl = "v1/rest/object/{apiName}/action/Add".replace("{apiName}", objectApiName);
                        }
                    }
                    break;
                case update:
                    Set<String> notUsePaasUpdateApiName = tenantConfigurationManager.getNotUsePaasUpdateApiName(tenantId);
                    if (userPaasUpdateApiNames.contains(objectApiName) && !notUsePaasUpdateApiName.contains(objectApiName)) {
                        interfaceUrl = "v1/inner/rest/object_data/{apiName}/{objectId}?incrementalUpdate=true"
                                .replace("{apiName}", objectApiName);

                    } else {
                        if (CheckinsFieldConstants.API_NAME.equals(objectApiName)) {
                            interfaceUrl = "v1/rest/object/{apiName}/action/SysEdit".replace("{apiName}", objectApiName);
                        } else {
                            final boolean syncDetailFillMasterUpdate = syncDetailFillMasterUpdateApiNames.contains(objectApiName);
                            if (syncDetailFillMasterUpdate || (!DeliveryNoteApiName.contains(objectApiName)) || configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(mainObjectApiName)) {
                                interfaceUrl = "v1/rest/object/{apiName}/action/Edit".replace("{apiName}", mainObjectApiName);
                            } else {
                                interfaceUrl = "v1/rest/object/{apiName}/action/IncrementUpdate".replace("{apiName}", mainObjectApiName);
                            }
                        }
                    }
                    break;
                case invalid:
                    interfaceUrl = "v1/rest/object/{apiName}/action/BulkInvalid".replace("{apiName}", objectApiName);
                    break;
                case delete:interfaceUrl="v1/inner/rest/object_data/{apiName}/bulk_delete".replace("{apiName}", objectApiName);
                    break;
                case recover:
                default:
                    interfaceUrl = objectApiName;
            }
        }

        return interfaceUrl;
    }

    private void write2CrmSpeedLimit(SyncDataContextEvent message) {
        String tenantId = message.getDestTenantId();
        long counts = 1L;
        if (message.getDestDetailSyncDataIdAndDestDataMap() != null) {//累加明细数量
            counts = counts + message.getDestDetailSyncDataIdAndDestDataMap().size();
        }
        //统计数量，如果超了threshold自动按比例延长超时时间，不阻塞
        SpeedLimitTypeEnum toCrmType = SpeedLimitTypeEnum.TO_CRM;

        List<String> batchWrite2CRMObjAPiNameList = configCenterConfig.getBatchWrite2CrmByTenant(tenantId);
        String crmMasterObjectApiName = outerServiceFactory.get(TenantTypeEnum.CRM.getType()).getMasterObjectApiName(tenantId, TenantTypeEnum.CRM.getType(), message.getDestObjectApiName()).getData();
        if (null == crmMasterObjectApiName) {
            crmMasterObjectApiName = message.getDestObjectApiName();
        }
        if (batchWrite2CRMObjAPiNameList.contains(crmMasterObjectApiName)) {
            toCrmType = SpeedLimitTypeEnum.TO_CRM_BATCHWRITE;
        }
        TimePointRecorderStatic.setCountMsg(message.getSyncDataId(), Integer.valueOf(String.valueOf(counts)));
        log.debug("trace rate tenantId:{}, toCrmType:{}, sourceobj:{} write counts:{}", tenantId, toCrmType.getName() + toCrmType.getRedisKeyPrefix(),
                message.getDestObjectApiName(), counts);
        //crm当前不支持对象单独限速，所以这里的key, 可以直接传tenantId
        speedLimitManager.countAndCheck(tenantId, toCrmType, counts, false);
    }

    @InvokeMonitor(tenantId = "#tenantId", dcId = "", invokeType = InvokeTypeEnum.CRM, count = "#doWriteMqDataList?.size()?:0", data = "#doWriteMqDataList", objAPIName = "#objApiName", action = ActionEnum.BATCH_WRITE, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public Result<Void> batchDoWriteAndAfter(String tenantId, String objApiName, int eventType, List<SyncDataContextEvent> doWriteMqDataList) {
        Result<Void> result = batchDoWrite(tenantId, objApiName, eventType, doWriteMqDataList);
        afterBatchDoWrite(tenantId, doWriteMqDataList);
        return result;
    }

    public void afterBatchDoWrite(String tenantId, List<SyncDataContextEvent> doWriteMqDataList) {
        //批量更新中间表
        syncWriteMainManager.batchCompleteWriteUpdateStatus(doWriteMqDataList);
        for(SyncDataContextEvent syncStepData: doWriteMqDataList){//执行完成写逻辑
            if (completeDataWriteManager.needProcess(syncStepData)) {
                // 尝试加回traceId,让自定义函数的调用traceId正常
                LogIdUtil.reset(syncStepData.getSyncLogId());
                TraceUtil.initTrace(syncStepData.getSyncLogId());
                completeDataWriteManager.processMessage(syncStepData);
                if (syncStepData.isFinish()) {
                    String objectApiName = syncStepData.getObjectApiName();
                    String mainObjApiName = syncStepData.getMainObjApiName();
                    String dataId = syncStepData.getDataId();
                    Long version = syncStepData.getVersion();
                    String syncPloyDetailId = syncStepData.getSyncPloyDetailId();
                    monitorReportManager.sendNodeMsgByNodeTypeAndName(DataNodeTypeEnum.end, DataNodeNameEnum.DataEnd, tenantId, objectApiName, mainObjApiName, dataId, version, syncPloyDetailId, System.currentTimeMillis(), 1000, i18NStringManager.getByEi(I18NStringEnum.s2040, tenantId));
                    log.info("finish sync,msg:{}", syncStepData);
                }
            }
        }
    }

    public Result<Void> batchDoWrite(String tenantId, String objApiName, int eventType, List<SyncDataContextEvent> doWriteMqDataList) {
        CrmRequestBaseParam baseParam = buildCrmRequestBaseParam(tenantId, CrmConstants.SYSTEM_USER, objApiName);
        List<com.fxiaoke.open.erpsyncdata.common.data.ObjectData> dataList = doWriteMqDataList.stream().map(SyncDataContextEvent::getDestData).collect(Collectors.toList());
        final TriggerFlowConfig triggerFlowConfig = configCenterConfig.getTriggerFlowConfig(tenantId, objApiName);
        final Set<String> uncheckDependencyApiNames = configCenterConfig.getBatchWriteCrmUncheckDependencyObjectApiNames(tenantId);
        final boolean uncheckDependency = uncheckDependencyApiNames.contains("*") || uncheckDependencyApiNames.contains(objApiName);
        if (log.isInfoEnabled()) {
            final List<String> dataIds = dataList.stream().map(com.fxiaoke.open.erpsyncdata.common.data.ObjectData::getId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            final List<String> dataNames = dataList.stream().map(com.fxiaoke.open.erpsyncdata.common.data.ObjectData::getName).collect(Collectors.toList());
            log.info("batchWrite2Crm tenantId:{} objApiName:{} dataIds:{} dataNames:{} triggerFlowConfig:{} uncheckDependency:{}", tenantId, objApiName, dataIds, dataNames, triggerFlowConfig, uncheckDependency);
        }
        Result<Void> result = new Result<>();
        String errorMessage;
        if (eventType == EventTypeEnum.ADD.getType()) {
            try {
                final Set<String> needAddRelevantTeamApiNames = configCenterConfig.getNeedAddRelevantTeamObjectApiNames(tenantId);
                final boolean needAddRelevantTeam = needAddRelevantTeamApiNames.contains("*") || needAddRelevantTeamApiNames.contains(objApiName);
                if(needAddRelevantTeam){
                    for(com.fxiaoke.open.erpsyncdata.common.data.ObjectData objectData:dataList){
                        if(CollectionUtils.isEmpty(objectData.getOwner())){
                            continue;
                        }
                        TeamMemberInfoData teamMemberInfo=TeamMemberInfoData.builder().teamMemberEmployee(objectData.getOwner())
                                .teamMemberPermissionType("2").teamMemberType("0").teamMemberRole("1").teamMemberRoleList(Lists.newArrayList("1")).build();
                        objectData.put("relevant_team",Lists.newArrayList(teamMemberInfo));
                    }
                }
                BatchCreateObjectResult batchCreateObjectResult = crmRemoteService.batchCreateObject(objApiName, doWriteMqDataList, baseParam, dataList, triggerFlowConfig, uncheckDependency);
                log.debug("batchDoWrite,batchCreateObject,batchCreateObjectResult={}", batchCreateObjectResult);
                if (batchCreateObjectResult.isSuccess()) {
                    fillSuccessDoWriteResultData(doWriteMqDataList);
                } else {
//                    检查是否有部分数据已创建成功,构建doWriteResult
                    //过滤成功的数据，重新推送数据
                    errorMessage = batchCreateObjectResult.getErrorMessage();
                    fillCreateErrorDoWriteResultData(doWriteMqDataList, batchCreateObjectResult.getErrorCode(), errorMessage,true);
                    result.setCode(-1);
                    result.setMessage(errorMessage);
                }
            } catch (Exception e) {
                log.info("batchDoWrite,batchCreateObject Exception tenantId={} objApiName={} doWriteMqDataList={} e={}", tenantId, objApiName, doWriteMqDataList, e);
                errorMessage = e.getMessage();
                fillCreateErrorDoWriteResultData(doWriteMqDataList, 0, errorMessage,true);
                result.setCode(-1);
                result.setMessage(errorMessage);

                if (e instanceof SocketTimeoutException || e instanceof ConnectException) {                    
                    speedLimitManager.countAndCheck(tenantId, SpeedLimitTypeEnum.TO_CRM_BATCHWRITE, (long)dataList.size(), true);
                    // 记录超时监控
                    for (SyncDataContextEvent event : doWriteMqDataList) {
                        TimePointRecorderStatic.recordSync("networkTimeout", event.getSyncDataId());
                    }
                }
            }
        } else if (eventType == EventTypeEnum.UPDATE.getType()) {
            try {
                BatchUpdateObjectResult batchUpdateObjectResult = crmRemoteService.batchUpdateObject(objApiName, doWriteMqDataList, baseParam, dataList, triggerFlowConfig, uncheckDependency);
                log.debug("batchDoWrite,batchUpdateObject,batchUpdateObjectResult={}", batchUpdateObjectResult);
                if (batchUpdateObjectResult.isSuccess()) {
                    fillSuccessDoWriteResultData(doWriteMqDataList);
                } else {
                    errorMessage = batchUpdateObjectResult.getErrorMessage();
                    fillUpdateErrorDoWriteResultData(doWriteMqDataList, errorMessage);
                    result.setCode(-1);
                    result.setMessage(errorMessage);
                }
            } catch (Exception e) {
                log.info("batchDoWrite,batchUpdateObject Exception tenantId={} objApiName={} doWriteMqDataList={} e={}", tenantId, objApiName, doWriteMqDataList, e);
                errorMessage = e.getMessage();
                fillUpdateErrorDoWriteResultData(doWriteMqDataList, e.getMessage());
                result.setCode(-1);
                result.setMessage(errorMessage);

                if (e instanceof SocketTimeoutException || e instanceof ConnectException) {                    
                    speedLimitManager.countAndCheck(tenantId, SpeedLimitTypeEnum.TO_CRM_BATCHWRITE, (long)dataList.size(), true);
                    // 记录超时监控
                    for (SyncDataContextEvent event : doWriteMqDataList) {
                        TimePointRecorderStatic.recordSync("networkTimeout", event.getSyncDataId());
                    }
                }
            }
        }
        for (SyncDataContextEvent doWriteResultData : doWriteMqDataList) {
            log.debug("batchDoWrite,doWriteResultData={}", doWriteResultData);
            this.afterWrite(doWriteResultData);

            syncDataManager.fillSimpleSyncData(doWriteResultData.getSyncDataMap(), doWriteResultData);
            doWriteResultData.next();
        }
        return result;
    }

    private CrmRequestBaseParam buildCrmRequestBaseParam(String tenantId, Integer employeeId, String apiName) {
        return new CrmRequestBaseParam(Integer.valueOf(tenantId), employeeId, apiName);
    }

    private void fillSuccessDoWriteResultData(List<SyncDataContextEvent> doWriteMqDataList) {

        for (SyncDataContextEvent doWriteMqData : doWriteMqDataList) {
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            writeResult.setSyncDataId(doWriteMqData.getSyncDataId());
            writeResult.setDestDataId(doWriteMqData.getDestDataId());
            doWriteMqData.setWriteResult(writeResult);
        }
    }

    private void fillUpdateErrorDoWriteResultData(List<SyncDataContextEvent> doWriteMqDataList, String errMsg) {
        doWriteMqDataList.forEach(doWriteMqData -> {
            doWriteMqData.newError(doWriteMqData.getDestEventType(), doWriteMqData.getSyncDataId(), i18NStringManager.getByEi(I18NStringEnum.s951, doWriteMqData.getTenantId()) + errMsg);
        });
    }

    private void fillCreateErrorDoWriteResultData(List<SyncDataContextEvent> doWriteMqDataList, int errorCode, String errMsg, boolean isBatchWrite) {
        processCreateObjectFail(doWriteMqDataList, errorCode, errMsg,isBatchWrite);
    }
}