package com.fxiaoke.open.erpsyncdata.preprocess.aop;

import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.TriggerFlowConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService;
import com.fxiaoke.retrofit2.http.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 记录crm写接口的interface_monitor
 *
 * <AUTHOR>
 * @date 2024/8/29 10:05:23
 */
@Component
@Aspect
@Slf4j
public class SaveInterfaceMonitorAspect {

    @Autowired
    private OverrideOuterService overrideOuterService;

    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired()
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;

    private static ThreadLocal<SyncDataContextEvent> contextEventThreadLocal = new ThreadLocal<>();

    public static void setContextEvent(SyncDataContextEvent syncDataContextEvent) {
        contextEventThreadLocal.set(syncDataContextEvent);
    }

    public static void cleanContextEvent() {
        contextEventThreadLocal.remove();
    }

    Map<Method, BiFunction<SyncDataContextEvent, Object[], MethodInfo>> functionMap = Maps.newConcurrentMap();

    @Around("execution(* com.fxiaoke.crmrestapi.service.ObjectDataService.updateObjectData(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.changeOwner(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.edit(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.incrementUpdate(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.bulkInvalid(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.ObjectDataService.bulkDelete(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.ObjectDataService.create(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.sysAdd(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.bulkCreate(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.add(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.sysEdit(..))")
    public Result<?> saveCrmWriteInterfaceMonitor(ProceedingJoinPoint jp) throws Throwable {
        final SyncDataContextEvent syncDataContextEvent = contextEventThreadLocal.get();
        final Method method = ((MethodSignature) jp.getSignature()).getMethod();

        Result<?> invoke = null;
        int status = 1;
        long callTime = 0;
        MethodInfo methodInfo = new MethodInfo(null, null, null);
        try {
            final BiFunction<SyncDataContextEvent, Object[], MethodInfo> function = functionMap.computeIfAbsent(method, this::getMethodInfoFunc);
            methodInfo = function.apply(syncDataContextEvent, jp.getArgs());
        } catch (Exception e) {
            log.error("获取interface_monitor参数失败", e);
        }

        try {
            callTime = System.currentTimeMillis();
            invoke = (Result<?>) jp.proceed(jp.getArgs());
            return invoke;
        } catch (Exception e) {
            invoke = new Result<>();
            invoke.setCode(5000000);
            String msg;
            if (e instanceof CrmBusinessException) {
                msg = e.getMessage();
            } else {
                //由于展示有限，只打印最底层异常
                Throwable rootCause = ExceptionUtil.getRootCause(e);
                String stackTrace = null;
                if (rootCause != null) {
                    //将caused by 抛出
                    StringWriter sw = new StringWriter();
                    PrintWriter pw = new PrintWriter(sw);
                    rootCause.printStackTrace(pw);
                    stackTrace = sw.toString();
                }
                msg = i18NStringManager.getByEi(I18NStringEnum.s952, methodInfo.getTenantId()) + ",exception " +
                        Optional.ofNullable(stackTrace).map((s) -> s.length() > 500 ? s.substring(0, 500) : s).orElse(" null exception");
                status = 2;
            }
            invoke.setMessage(msg);
            throw e;
        } finally {
            // erp-sync-data-file(UpLoadFileServiceImpl.updateCrmObj) 不支持
            if (Objects.nonNull(syncDataContextEvent)) {
                overrideOuterService.saveInterfaceLog(methodInfo.getUrl(), syncDataContextEvent, methodInfo.getArg(), invoke, callTime, System.currentTimeMillis(), status);
            }
        }
    }

    @AllArgsConstructor
    @Data
    private static class MethodInfo {
        private String tenantId;
        private String url;
        private Object arg;
    }

    private static final List<Pair<Class<? extends Annotation>, Function<Annotation, String>>> annotationFunctionList = Lists.newArrayList(
            Pair.of(PUT.class, put -> ((PUT) put).value()),
            Pair.of(POST.class, post -> ((POST) post).value()),
            Pair.of(GET.class, get -> ((GET) get).value()),
            Pair.of(DELETE.class, delete -> ((DELETE) delete).value()),
            Pair.of(PATCH.class, patch -> ((PATCH) patch).value()));

    private BiFunction<SyncDataContextEvent, Object[], MethodInfo> getMethodInfoFunc(Method method) {
        String urlFormat = annotationFunctionList.stream()
                .map(pair -> {
                    final Annotation annotation = method.getAnnotation(pair.getKey());
                    return Objects.nonNull(annotation) ? pair.getValue().apply(annotation) : null;
                }).filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("not found annotation, method:" + method.getName()));
        final Parameter[] parameters = method.getParameters();
        Map<String, Integer> pathIndexMap = new HashMap<>();
        Integer argIndex = null;
        Map<String, Integer> paramIndexMap = Maps.newLinkedHashMap();
        Integer triggerWorkFlowIndex = null;
        Integer triggerFlowIndex = null;
        Integer headerMapIndex = null;
        Integer objectApiNameIndex = null;
        for (int i = 0; i < parameters.length; i++) {
            final Parameter parameter = parameters[i];
            if (Objects.nonNull(parameter.getAnnotation(HeaderMap.class))) {
                headerMapIndex = i;
                continue;
            }
            final Path path = parameter.getAnnotation(Path.class);
            if (Objects.nonNull(path)) {
                final String value = path.value();
                if (StringUtils.equalsIgnoreCase("describeAPIName", value) || StringUtils.equalsIgnoreCase("apiName", value)) {
                    objectApiNameIndex = i;
                }
                pathIndexMap.put(value, i);
                continue;
            }
            if (Objects.nonNull(parameter.getAnnotation(Body.class))) {
                argIndex = i;
                continue;
            }
            final Query query = parameter.getAnnotation(Query.class);
            if (Objects.nonNull(query)) {
                // 处理流程
                final String queryParameter = query.value();
                if (StringUtils.equalsIgnoreCase("triggerWorkflow", queryParameter)) {
                    triggerWorkFlowIndex = i;
                } else if (StringUtils.equalsIgnoreCase("triggerFlow", queryParameter)) {
                    triggerFlowIndex = i;
                }
                paramIndexMap.put(queryParameter, i);
            }
        }

        // ** updateObjectData接口的triggerFlow代表的是工作流...为了防止混淆，这里做一下处理
        // ** updateObjectData接口udobj实现有问题,无法支持,先屏蔽掉
        if (StringUtils.equalsIgnoreCase("updateObjectData", method.getName())) {
//            triggerWorkFlowIndex = triggerFlowIndex;
            triggerFlowIndex = null;
//            paramIndexMap.put("triggerWorkFlow", paramIndexMap.get("triggerFlow"));
            paramIndexMap.remove("triggerFlow");
        }

        return getMethodInfoFunc(urlFormat, triggerFlowIndex, triggerWorkFlowIndex, argIndex, headerMapIndex, objectApiNameIndex, pathIndexMap, paramIndexMap);
    }

    private @NotNull BiFunction<SyncDataContextEvent, Object[], MethodInfo> getMethodInfoFunc(String urlFormat, Integer triggerFlowIndex, Integer triggerWorkFlowIndex, Integer argIndex, Integer headerMapIndex, Integer objectApiNameIndex, Map<String, Integer> pathIndexMap, Map<String, Integer> paramIndexMap) {
        String first = urlFormat.contains("?") ? "&" : "?";
        return (contextEvent, args) -> {
            final String tenantId = Optional.ofNullable(contextEvent)
                    .map(SyncDataContextEvent::getTenantId)
                    .orElseGet(() -> Optional.ofNullable(headerMapIndex)
                            .map(index -> args[index])
                            .map(o -> ((HeaderObj) o).get("X-fs-ei"))
                            .map(String::valueOf)
                            .orElse(null));
            if (Objects.nonNull(tenantId) && (Objects.nonNull(triggerFlowIndex) || Objects.nonNull(triggerWorkFlowIndex))) {
                final String crmObjApiName = Optional.ofNullable(contextEvent)
                        .map(SyncDataContextEvent::getCrmObjApiName)
                        .orElseGet(() -> Optional.ofNullable(objectApiNameIndex)
                                .map(index -> args[index])
                                .map(String::valueOf)
                                .map(apiName -> {
                                    final String name = syncPloyDetailSnapshotManager.getCrmMasterObjectApiName(tenantId, apiName).getData();
                                    return Objects.nonNull(name) ? name : apiName;
                                }).orElse(null));
                if (Objects.nonNull(crmObjApiName)) {
                    final TriggerFlowConfig triggerFlowConfig = configCenterConfig.getTriggerFlowConfig(tenantId, crmObjApiName);
                    if (Objects.nonNull(triggerFlowIndex)) {
                        args[triggerFlowIndex] = triggerFlowConfig.checkTriggerFlow();
                    }
                    if (Objects.nonNull(triggerWorkFlowIndex)) {
                        args[triggerWorkFlowIndex] = triggerFlowConfig.checkTriggerWorkFlow();
                    }
                }
            }

            if (Objects.isNull(contextEvent)) {
                // 没有contextEvent,只需要改触发流程参数
                return new MethodInfo(tenantId, null, null);
            }

            String url = urlFormat;
            for (Map.Entry<String, Integer> entry : pathIndexMap.entrySet()) {
                String key = entry.getKey();
                Integer i = entry.getValue();
                url = url.replace("{" + key + "}", args[i].toString());
            }

            final String param = paramIndexMap.entrySet().stream()
                    .filter(entry -> Objects.nonNull(args[entry.getValue()]))
                    .map(entry -> entry.getKey() + "=" + args[entry.getValue()])
                    .collect(Collectors.joining("&"));

            if (StringUtils.isNotBlank(param)) {
                url = url + first + param;
            }

            return new MethodInfo(tenantId, url, Objects.nonNull(argIndex) ? args[argIndex] : null);
        };
    }
}