package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionBulkInvalidArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.BulkDeleteArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SpuSkuConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConvertFactory;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConverter;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.OptionMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CompleteDataWriteArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.NeedReturnData2SyncDataManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.SpecialFieldPreprocessManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmRequestBaseParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SpecAndValue;
import com.fxiaoke.open.erpsyncdata.preprocess.result.CrmObjectDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncSkuSpuService;
import com.fxiaoke.open.erpsyncdata.preprocess.util.GrayUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.util.SpecUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.util.SpuSkuUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;

/**
 * <AUTHOR>
 * @Date: 9:58 2020/10/22
 * @Desc: 商品产品处理
 */
@Service
@Slf4j
public class SyncSkuSpuServiceImpl implements SyncSkuSpuService {
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private FieldConvertFactory fieldConvertFactory;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private ErpDataService erpDataService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private SpecialFieldPreprocessManager specialFieldPreprocessManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private IdFieldKeyManager idFieldKeyManager;
    @Autowired
    private ErpOverrideOuterServiceImpl overrideOuterService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private NeedReturnData2SyncDataManager needReturnData2SyncDataManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;

    @Override
    public SyncDataContextEvent handleSkuSpu2Crm(SyncDataContextEvent message) {
        String tenantId = message.getDestTenantId();
        String dcId = dataCenterManager.getDataCenterBySnapshotId(tenantId, message.getSyncPloyDetailSnapshotId());
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        if (!isV1Version(tenantId)) {
            return checkDestData(message, tenantId);
        }
        //开启商品走特殊逻辑
        switch (connectInfo.getChannel()) {
            case ERP_SAP:
            case STANDARD_CHANNEL:
                return handleSap(message, tenantId, dcId);
            case ERP_K3CLOUD:
                return handleK3Cloud(message, tenantId, dcId);
            default:
                return message;
        }
    }

    private SyncDataContextEvent checkDestData(SyncDataContextEvent message, String tenantId) {
        //新版本，自动创建商品，不需要特殊处理
        if (EventTypeEnum.UPDATE.match(message.getDestEventType())) {
            //检查是否允许修改产品名称
            Boolean allowUpdateName = tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.ALLOW_UPDATE_PRODUCT_NAME_WHILE_OPEN_SPU);
            if (allowUpdateName) {
                return message;
            }
            String isOpenSpuValue = sfaApiManager.querySpuOpenStatus(tenantId, false);
            if (IsOpenSpuEnum.IsOpen.getValue().equals(isOpenSpuValue)) {
                //开启了商品不允许更新产品名称。
                ObjectData destData = message.getDestData();
                if (destData != null) {
                    destData.remove("name");
                }
            }
        }
        return message;
    }

    private boolean isV1Version(String tenantId) {
        //先检查SFA配置
        boolean autoGenerateSpu = GrayUtil.autoGenerateSpu(tenantId);
        if (!autoGenerateSpu) {
            //未灰度自动生成商品的，按v1处理
            return true;
        }
        //在配置表设置一个，当代码发布后，才会设置上去的时间。
        return tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.SPU_V1_TENANT_LIST);
    }

    private SyncDataContextEvent doWriteAndStop(SyncDataContextEvent message) {
        SyncDataContextEvent doWriteResultDataResult = this.doWrite(message);//更新或者新增产品
        doWriteResultDataResult.setStop(true);
        return doWriteResultDataResult;
    }

    private SyncDataContextEvent handleK3Cloud(SyncDataContextEvent message, String tenantId, String erpCenterId) {
        log.info("SyncSkuSpuServiceImpl.handleK3Cloud,message={}", message);
        ObjectData destData = message.getDestData();
        if (destData == null) {
            //destData为空不处理
            return message;
        }
        String isOpenSpu = sfaApiManager.querySpuOpenStatus(tenantId,false);
        if (isOpenSpu == null) {
            buildDoWriteResultData(message, -1,
                    i18NStringManager.getByEi(I18NStringEnum.s1268,tenantId));
            return message;
        }
        log.info("SyncSkuSpuServiceImpl.handleK3Cloud,destData={}", destData);

        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        Boolean isMultipleUnit = false;
        if (isOpenMultipleUnit(tenantId, message.getSyncPloyDetailSnapshotId())) {
            isMultipleUnit = true;
        }

        boolean is_spec = false;
        String product_spec = destData.getString("product_spec");
        Boolean useSpecification = tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.K3_USE_SPECIFICATION);
        if (useSpecification
                && StringUtils.isNotEmpty(product_spec)) {
            log.info("SyncSkuSpuServiceImpl.handleK3Cloud,tenantId={} has enabled specification sync,is_spec=true", tenantId);
            is_spec = true;
        }

        //不开启直接更新或者新增产品
        if (IsOpenSpuEnum.IsNotOpen.getValue().equals(isOpenSpu)) {
            //如果开启了多单位
            if (isMultipleUnit) {
                destData.put("is_multiple_unit", true);
                if (message.getDestEventType() == 1) {//新增
                    if (destData.get("unit") != null) {
                        Map<String, Object> baseUnit = Maps.newHashMap();
                        baseUnit.put("is_base", true);
                        baseUnit.put("is_enable", true);
                        baseUnit.put("is_pricing", true);
                        baseUnit.put("conversion_ratio", "1");
                        baseUnit.put("unit_id", destData.get("unit"));
                        destData.put("multi_unit_data", Lists.newArrayList(baseUnit));
                    }
                } else if (message.getDestEventType() == 2) {//更新
                    List<com.fxiaoke.crmrestapi.common.data.ObjectData> unitList = getMultiUnitList(tenantId, destData.getId());
                    if(CollectionUtils.isEmpty(unitList)){//为了兼容历史数据，就是没有多单位的
                        destData.put("is_multiple_unit", false);
                    }else {
                        destData.put("multi_unit_data", unitList);
                    }
                }
            }
            return doWriteAndStop(message);
        }
        SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, message.getSyncPloyDetailSnapshotId()).getData().getSyncPloyDetailData();
        //K3渠道商品处理
        String productId = message.getDestDataId();
        log.info("SyncSkuSpuServiceImpl.handleK3Cloud,productId={}", productId);
        com.fxiaoke.crmrestapi.common.data.ObjectData productObjectData = SpuSkuUtils.queryProductDataByProductId(objectDataService,
                tenantId,
                productId,
                i18NStringManager);

        String spuName = destData.getName();
        if (destData.getName().contains("#")) {
            List<String> items = Splitter.on("#").splitToList(destData.getName());
            spuName = items.get(items.size() - 1);
        }
        log.info("SyncSkuSpuServiceImpl.handleK3Cloud,destData.getName()={},spuName={}", destData.getName(), spuName);
        com.fxiaoke.crmrestapi.common.data.ObjectData spuObjectData = SpuSkuUtils.querySpuDataBySpuName(metadataControllerService,
                tenantId,
                spuName,
                i18NStringManager);

        //商品和产品都不存在 或者 客户不在使用规格的企业名单里面
        if ((spuObjectData == null && productObjectData == null) || (!useSpecification && productObjectData == null)) {
            if (is_spec) {
                assembleSpecData(tenantId, destData, spuName);
            }
            //产品不存在，取部分参数，创建商品和产品
            //新增商品产品
            com.fxiaoke.crmrestapi.common.data.ObjectData spuObj = new com.fxiaoke.crmrestapi.common.data.ObjectData(ObjectApiNameEnum.FS_SPU.getObjApiName());
            spuObj.put("sku", Collections.singletonList(destData));
            spuObj.setTenantId(Integer.valueOf(tenantId));
            if (is_spec) {
                spuObj.setName(spuName);//商品名称不带编码
            } else {
                spuObj.setName(destData.getName());//商品名称不带编码
            }

            spuObj.put("owner", destData.getOwner());
            spuObj.put("created_by", destData.getCreatedBy());
            spuObj.put("standard_price", destData.get("price"));
            if(Objects.nonNull(destData.get("category"))){
                spuObj.put("category", destData.get("category"));
            }
            if(Objects.nonNull(destData.get("product_category_id"))){
                spuObj.put("product_category_id", destData.get("product_category_id"));
            }
            spuObj.put("unit", destData.get("unit"));
            spuObj.put("is_spec", is_spec);
            spuObj.put("record_type", "default__c");
            //添加是否开启批次序列号
            spuObj.put("batch_sn", destData.get("batch_sn"));
            //如果开启了多单位
            if (isMultipleUnit) {
                destData.put("is_multiple_unit", true);
                spuObj.put("is_multiple_unit", true);
                if (spuObj.get("unit") != null) {
                    Map<String, Object> baseUnit = Maps.newHashMap();
                    baseUnit.put("is_base", true);
                    baseUnit.put("is_enable", true);
                    baseUnit.put("is_pricing", true);
                    baseUnit.put("conversion_ratio", "1");
                    baseUnit.put("unit_id", destData.get("unit"));
                    spuObj.put("multi_unit_data", Lists.newArrayList(baseUnit));
                }
            }
            Result<ActionAddResult> spuAddResult = this.createCrmObjectData(headerObj, spuObj, new HashMap<>(), message);
            if (!spuAddResult.isSuccess()) {//新增商品产品失败
                log.info("create spu failed arg={},result={}", spuObj, spuAddResult);
                return buildErrorResult(message,
                        i18NStringManager.getByEi(I18NStringEnum.s1269,tenantId) + spuAddResult.getMessage() + ":errCode=" + spuAddResult.getCode());
            }
            if(needReturnData2SyncDataManager.hasNeedReturnData2SyncDataNode(syncPloyDetailData,ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName())){
                com.fxiaoke.crmrestapi.common.data.ObjectData productData = SpuSkuUtils.queryProductDataByProductId(objectDataService,
                        tenantId,
                        productId,
                        i18NStringManager);
                if (productData != null) {
                    needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(tenantId, message.getSyncDataId(), productData, syncPloyDetailData);
                }
            }
            String spuId = spuAddResult.getData().getObjectData().getId();
            destData.put("spu_id", spuId);
            return buildSuccessResult(message);
        } else {
            //产品已创建，更换商品id
            String spuId = null;
            if (productObjectData != null) {
                spuId = productObjectData.getString("spu_id");
            } else {
                spuId = spuObjectData.getId();
            }
            destData.put("spu_id", spuId);
            //更新产品必传字段，并且字段的类型只能是数字
            destData.put("status_flag", 1);

            com.fxiaoke.crmrestapi.common.data.ObjectData spuData = SpuSkuUtils.querySpuDataBySpuId(objectDataService,
                    tenantId,
                    spuId,
                    i18NStringManager);
            //上面有可能返回null，这里判空
            if (spuData == null) {
                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s261.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s261.getI18nValue(), spuId),
                        Lists.newArrayList(spuId)),
                        null,
                        null);
            }
            if (is_spec) {
                assembleSpecData(tenantId, destData, spuName);
                //更新产品规格必传字段，并且字段的类型只能是数字
                destData.put("status_flag", 5);
            }

            //和hardy确认，开启商品同步的情况下，更新商品不支持改名字，主要是为了解决多个重名的物料对一个商品的问题
            spuData.put("name", spuData.getName());
            spuData.put("standard_price", destData.get("price"));
            if(Objects.nonNull(destData.get("product_category_id"))){
                spuData.put("product_category_id", destData.get("product_category_id"));
            }
            if(Objects.nonNull(destData.get("category"))){
                spuData.put("category", destData.get("category"));
            }
            spuData.put("unit", destData.get("unit"));


            List<com.fxiaoke.crmrestapi.common.data.ObjectData> productList = SpuSkuUtils.querySpuRelatedList(tenantId,
                    spuId,
                    metadataControllerService,
                    i18NStringManager);

            log.info("SyncSkuSpuServiceImpl.handleK3Cloud,destData2={}", destData);
            if (is_spec) {
                assembleProductData(tenantId, destData, spuName, productList, metadataControllerService);
                spuData.put("sku", productList);
            } else {
                spuData.put("sku", Lists.newArrayList(destData));
            }

            if (isMultipleUnit) {
                destData.put("is_multiple_unit", true);

                spuData.put("is_multiple_unit", true);
                List<com.fxiaoke.crmrestapi.common.data.ObjectData> unitList = getMultiUnitListBySpuId(tenantId, spuId);
                spuData.put("multi_unit_data", unitList);
            }

            Result<ActionEditResult> updateSpuResult = updateCrmObjectData(headerObj,
                    ObjectApiNameEnum.FS_SPU.getObjApiName(),
                    spuData,
                    message);
            if(needReturnData2SyncDataManager.hasNeedReturnData2SyncDataNode(syncPloyDetailData,ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName())){
                com.fxiaoke.crmrestapi.common.data.ObjectData productData = SpuSkuUtils.queryProductDataByProductId(objectDataService,
                        tenantId,
                        productId,
                        i18NStringManager);
                if (productData != null) {
                    needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(tenantId, message.getSyncDataId(), productData, syncPloyDetailData);
                }
            }
            if (!updateSpuResult.isSuccess()) {//新增商品产品失败
                log.info("SyncSkuSpuServiceImpl.handleK3Cloud,update spu failed spuData={},result={}", spuData, updateSpuResult);
                return buildErrorResult(message,
                        i18NStringManager.getByEi(I18NStringEnum.s1270,tenantId) + updateSpuResult.getMessage() + ":errCode=" + updateSpuResult.getCode());
            }
            return buildSuccessResult(message);

            //return doWriteAndStop(message);
        }
    }

    /**
     * @param tenantId
     * @param destData                  当前产品数据
     * @param productList               商品下的产品列表
     * @param productName               当前产品的名称
     * @param metadataControllerService
     */
    public void assembleProductData(String tenantId,
                                    ObjectData destData,
                                    String productName,
                                    List<com.fxiaoke.crmrestapi.common.data.ObjectData> productList,
                                    MetadataControllerService metadataControllerService) {
        log.info("SyncSkuSpuServiceImpl.assembleProductData,destData={}", destData);
        int index = -1;
        for (int i = 0; i < productList.size(); i++) {
            com.fxiaoke.crmrestapi.common.data.ObjectData productData = productList.get(i);
            productData.put("status_flag", 1);
            String product_spec = productData.getString("product_spec");
            log.info("SyncSkuSpuServiceImpl.assembleProductData,product_spec={}", product_spec);
            String specName = Splitter.on(":").splitToList(product_spec).get(0);
            log.info("SyncSkuSpuServiceImpl.assembleProductData,specName={}", specName);

            com.fxiaoke.crmrestapi.common.data.ObjectData specData = SpecUtils.querySpec(tenantId, specName, metadataControllerService,i18NStringManager);
            log.info("SyncSkuSpuServiceImpl.assembleProductData,specData={}", specData);

            String specId = specData.getId();
            List<com.fxiaoke.crmrestapi.common.data.ObjectData> specValueList = SpecUtils.querySpecRelatedList(tenantId,
                    specId,
                    metadataControllerService,
                    i18NStringManager);

            String specValueName = Splitter.on(":").splitToList(product_spec).get(1);
            log.info("SyncSkuSpuServiceImpl.assembleProductData,specValueName={}", specValueName);
            com.fxiaoke.crmrestapi.common.data.ObjectData specValueData = SpecUtils.getSpecValueData(specValueList, specValueName);
            log.info("SyncSkuSpuServiceImpl.assembleProductData,specValueData={}", specValueData);

            Map<String, String> specValueMap = new HashMap<>();
            specValueMap.put("spec_id", specId);
            specValueMap.put("spec_value_id", specValueData.getId());
            specValueMap.put("spec_value_name", specValueData.getName());
            specValueMap.put("order_field", "0");

            productData.put("spec_and_value", Lists.newArrayList(specValueMap));

            if (productData.getId().equalsIgnoreCase(destData.getId())) {
                index = i;
            }
        }
        if (index != -1) {
            //删除商品下指定产品的特殊标识
            //productList.get(index).put("status_flag",3);
            productList.remove(index);
            destData.put("status_flag", 1);
        } else {
            //在商品下新增产品的特殊标识
            destData.put("status_flag", 2);
            destData.put("nameIsFrozen", true);
            List<Map<String, String>> specAndValueList = (List<Map<String, String>>) destData.get("spec_and_value");
            destData.put(productName, specAndValueList.get(0).get("spec_value_id"));
            destData.put("nameIsFrozen", true);
            destData.remove("product_spec");
        }
        productList.add(com.fxiaoke.crmrestapi.common.data.ObjectData.convert(destData));
        log.info("SyncSkuSpuServiceImpl.assembleProductData,productList={}", productList);
    }

    private void assembleSpecData(String tenantId, ObjectData destData, String spuName) {
        String product_spec = destData.getString("product_spec");
        //默认用物料名称作为规格的名称
        //用物料的名称加规格值作为产品的名称
        String productName = spuName;
        destData.put("name", productName + "#" + product_spec);
        //创建规格
        Result<ActionAddResult> specResult = SpecUtils.createSpec(tenantId, destData.getOwner(), spuName,
                Lists.newArrayList(product_spec), metadataActionService,i18NStringManager);

        String specId = null;
        List<com.fxiaoke.crmrestapi.common.data.ObjectData> specValueList = new ArrayList<>();
        if (!specResult.isSuccess()) {
            //创建规格失败，新增的规格已存在
            if (specResult.getCode() == 320001401) {
                //查询存在的规格信息
                com.fxiaoke.crmrestapi.common.data.ObjectData specData = SpecUtils.querySpec(tenantId,
                        spuName,
                        metadataControllerService,
                        i18NStringManager);
                specId = specData.getId();

                //查询规格下的规格值列表
                List<com.fxiaoke.crmrestapi.common.data.ObjectData> list = SpecUtils.querySpecRelatedList(tenantId,
                        specId,
                        metadataControllerService,
                        i18NStringManager);
                com.fxiaoke.crmrestapi.common.data.ObjectData specValueData = SpecUtils.getSpecValueData(list, product_spec);
                if (specValueData == null) {
                    //在规格值列表找不到指定的规格值，需要新建规格值
                    Result<ActionAddResult> createSpecValueResult = SpecUtils.createSpecValue(tenantId,
                            product_spec,
                            specId,
                            metadataActionService,
                            i18NStringManager);
                    specValueData = createSpecValueResult.getData().getObjectData();
                }
                specValueList.add(specValueData);
            }
        } else {
            specId = specResult.getData().getObjectData().getId();
            specValueList = specResult.getData().getDetails().get(ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName());
        }

        //组装产品的规格字段信息
        List<Map<String, String>> specAndValueList = new ArrayList<>();
        for (int i = 0; i < specValueList.size(); i++) {
            Map<String, String> specValueMap = new HashMap<>();
            specValueMap.put("spec_id", specId);
            specValueMap.put("spec_value_id", specValueList.get(i).getId());
            specValueMap.put("spec_value_name", specValueList.get(i).getName());
            specValueMap.put("order_field", "0");
            specAndValueList.add(specValueMap);
        }
        destData.put("spec_and_value", specAndValueList);
    }

    private boolean isOpenMultipleUnit(String tenantId, String syncPloyDetailSnapshotId) {
        SyncPloyDetailSnapshotEntity ployDetailSnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId,syncPloyDetailSnapshotId);
        String dataCenterId = null;
        if (ployDetailSnapshotEntity != null) {
            String sourceObjectApiName = ployDetailSnapshotEntity.getSyncPloyDetailData().getSourceObjectApiName();
            dataCenterId = idFieldConvertManager.getDataCenterId(tenantId, sourceObjectApiName);
        }
        if (dataCenterId == null) {
            return false;
        }
        ErpTenantConfigurationEntity isOpenMultipleUnit = tenantConfigurationManager.findOne(tenantId, dataCenterId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.productNeedHandleMultipleUnit.name());
        if (isOpenMultipleUnit == null) {
            ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
            entity.setId(idGenerator.get());
            entity.setTenantId(tenantId);
            entity.setDataCenterId(dataCenterId);
            entity.setChannel(ErpChannelEnum.ERP_K3CLOUD.name());
            entity.setType(TenantConfigurationTypeEnum.productNeedHandleMultipleUnit.name());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdateTime(entity.getCreateTime());
            entity.setConfiguration("false");
            com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<JSONObject> jsonObjectResult = crmRemoteService.checkModuleStatus(tenantId, CrmConfigKeyConstants.MULTIPLE_UNIT);
            //如果开启了多单位
            if (jsonObjectResult != null && jsonObjectResult.isSuccess() && jsonObjectResult.getData().getJSONObject("value") != null &&
                    MultipleUnitStatusEnum.IsMultipleUnitOpen.getValue().equals(jsonObjectResult.getData().getJSONObject("value").getString("openStatus"))) {
                entity.setConfiguration("true");
            }
            tenantConfigurationManager.insert(tenantId,entity);
            isOpenMultipleUnit = tenantConfigurationManager.findOne(tenantId, dataCenterId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.productNeedHandleMultipleUnit.name());
        }
        if (isOpenMultipleUnit == null) {
            return false;
        }
        return Boolean.valueOf(isOpenMultipleUnit.getConfiguration());
    }

    private List<com.fxiaoke.crmrestapi.common.data.ObjectData> getMultiUnitList(String tenantId, String productId) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        Filter filter = new Filter();
        filter.setFieldName("product_id");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList(productId));
        searchQuery.setFilters(Lists.newArrayList(filter));
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<com.fxiaoke.crmrestapi.common.data.ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), controllerListArg);
        if (list != null && list.getData() != null && CollectionUtils.isNotEmpty(list.getData().getDataList())) {
            return list.getData().getDataList();
        }
        return Lists.newArrayList();
    }

    private List<com.fxiaoke.crmrestapi.common.data.ObjectData> getMultiUnitListBySpuId(String tenantId, String spuId) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        Filter filter = new Filter();
        filter.setFieldName("spu_id");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList(spuId));
        searchQuery.setFilters(Lists.newArrayList(filter));
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<com.fxiaoke.crmrestapi.common.data.ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), controllerListArg);
        if (list != null && list.getData() != null && CollectionUtils.isNotEmpty(list.getData().getDataList())) {
            return list.getData().getDataList();
        }
        return Lists.newArrayList();
    }

    //多数据中心可能会有问题
    private SyncDataContextEvent handleSap(SyncDataContextEvent syncDataContextEvent, String tenantId, String erpCenterId) {
//        GetConfigValueByKeyResult isOpenSpu = skuSpuService.getConfigValueByKey(headerObj, arg);
        String isOpenSpuValue = sfaApiManager.querySpuOpenStatus(tenantId,false);
        if (isOpenSpuValue == null) {
            buildDoWriteResultData(syncDataContextEvent, -1,
                    i18NStringManager.getByEi(I18NStringEnum.s1268,tenantId));
            return syncDataContextEvent;
        }
        //不开启直接更新或者新增产品
        if (IsOpenSpuEnum.IsNotOpen.getValue().equals(isOpenSpuValue)) {
            return doWriteAndStop(syncDataContextEvent);
        }
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        //查询规格策略明细
        String erpSpecValueFakeObjApiName = getErpSpecValueFakeObjApiName(tenantId, erpCenterId);
        /**
         * 如果开启了商品，完成商品产品的创建更新，阻断平台侧逻辑
         */
        //获取商品erp->crm策略明细；
        SyncPloyDetailEntity spuPloyDetailEntity = this.getPloyDetailByTypeAndTenantIdAndObjApiName(SyncPloyTypeEnum.INPUT.getType(), tenantId, ObjectApiNameEnum.FS_SPU.getObjApiName(), erpCenterId);
        if (spuPloyDetailEntity == null) {
            buildDoWriteResultData(syncDataContextEvent, -1, i18NStringManager.getByEi(I18NStringEnum.s1271,tenantId));
            return syncDataContextEvent;
        }
        //获取本次同步数据的源数据
        SyncDataEntity syncDataEntity = adminSyncDataDao.getSimple(tenantId, syncDataContextEvent.getSyncDataId());
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, syncDataEntity.getSourceObjectApiName());
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId(syncDataEntity.getSourceDataId());
        erpIdArg.setObjAPIName(relation.getErpRealObjectApiname());
        erpIdArg.setIncludeDetail(true);
        erpIdArg.setTenantId(syncDataEntity.getSourceTenantId());
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, relation.getDataCenterId());
        IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(tenantId, relation.getDataCenterId(), relation.getErpSplitObjectApiname(), relation.getErpRealObjectApiname());
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<SyncDataContextEvent> erpObjDataResult = erpDataService.getErpObjDataFromMongoIfExist(erpIdArg, relation.getDataCenterId(), idFieldKey);
        if (erpObjDataResult.getData() == null) {
            buildDoWriteResultData(syncDataContextEvent, -1,
                    i18NStringManager.getByEi2(I18NStringEnum.s1272.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s1272.getI18nValue(), erpIdArg.getDataId()),
                            Lists.newArrayList(erpIdArg.getDataId())) + i18NStringManager.get2(erpObjDataResult.getI18nKey(),null,tenantId,erpObjDataResult.getErrMsg(),erpObjDataResult.getI18nExtra()));
            return syncDataContextEvent;
        }
        Result<ObjectData> spuObjectDataDataResult = this.buildSpuObjectDataData(tenantId, syncDataContextEvent, erpObjDataResult.getData(), spuPloyDetailEntity, erpSpecValueFakeObjApiName,connectInfo);
        if (!spuObjectDataDataResult.isSuccess()) {
            buildDoWriteResultData(syncDataContextEvent, spuObjectDataDataResult.getCode(),
                    i18NStringManager.getByEi(I18NStringEnum.s1273,tenantId) + spuObjectDataDataResult.getMessage() + ":errCode=" + spuObjectDataDataResult.getCode());
            return syncDataContextEvent;
        }
        ObjectData spuObjectDataData = spuObjectDataDataResult.getData();
        if (StringUtils.isNotBlank(spuObjectDataData.getId())) {//更新商品，更新或者新增产品
            //更新商品，支持更新商品+产品
            String eis = configCenterConfig.getSAP_UPDATE_SPU_AND_SKU_EIS();
            if(!StringUtils.contains(eis,tenantId)) {
                //只更新商品不更新产品
                spuObjectDataData.remove("sku");
            }
            Result<String> updateSpuResult = this.updateSpuObj(Integer.valueOf(tenantId), spuObjectDataData);
            if (!updateSpuResult.isSuccess()) {//更新商品失败
                log.info("update spu failed arg={},result={}", spuObjectDataData, updateSpuResult);
                buildDoWriteResultData(syncDataContextEvent, updateSpuResult.getCode(),
                        i18NStringManager.getByEi(I18NStringEnum.s1274,tenantId) + updateSpuResult.getMessage() + ":errCode=" + updateSpuResult.getCode());
                return syncDataContextEvent;
            }
            //商品id
            syncDataContextEvent.getDestData().put("spu_id", spuObjectDataData.getId());
            if (syncDataContextEvent.getDestEventType().equals(2)) {//不更新产品名称
                syncDataContextEvent.getDestData().remove("name");
                syncDataContextEvent.getDestData().put("status_flag", 1);
            }
            syncDataContextEvent = this.doWrite(syncDataContextEvent);//更新或者新增产品
            syncDataContextEvent.setStop(true);
        } else {
            //新增商品产品
            spuObjectDataData.putId(IdUtil.generateId());
            com.fxiaoke.crmrestapi.common.data.ObjectData fsObjectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
            fsObjectData.putAll(spuObjectDataData);
            Result<ActionAddResult> spuAddResult = this.createCrmObjectData(headerObj, fsObjectData, new HashMap<>(), syncDataContextEvent);
            if (!spuAddResult.isSuccess()) {//新增商品产品失败
                log.info("create spu failed arg={},result={}", fsObjectData, spuAddResult);
                buildDoWriteResultData(syncDataContextEvent, spuAddResult.getCode(),
                        i18NStringManager.getByEi(I18NStringEnum.s1269,tenantId) + spuAddResult.getMessage() + ":errCode=" + spuAddResult.getCode());
                return syncDataContextEvent;
            }
            //产品id
            String productId = syncDataContextEvent.getDestData().getId();
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            writeResult.setSyncDataId(syncDataContextEvent.getSyncDataId());
            writeResult.setDestDataId(productId);
            syncDataContextEvent.setWriteResult(writeResult);
            syncDataContextEvent.setStop(true);
            SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, syncDataContextEvent.getSyncPloyDetailSnapshotId()).getData().getSyncPloyDetailData();
            if(needReturnData2SyncDataManager.hasNeedReturnData2SyncDataNode(syncPloyDetailData,ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName())){
                com.fxiaoke.crmrestapi.common.data.ObjectData productData = SpuSkuUtils.queryProductDataByProductId(objectDataService,
                        tenantId,
                        productId,
                        i18NStringManager);
                if (productData != null) {
                    needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(tenantId, syncDataContextEvent.getSyncDataId(), productData, syncPloyDetailData);
                }
            }
        }
        return syncDataContextEvent;
    }

    @Nullable
    private String getErpSpecValueFakeObjApiName(String tenantId, String erpCenterId) {
        String erpSpecValueFakeObjApiName = null;
        SyncPloyDetailEntity specificationPloyDetail = this.getPloyDetailByTypeAndTenantIdAndObjApiName(SyncPloyTypeEnum.INPUT.getType(), tenantId, ObjectApiNameEnum.FS_SPECIFICATIONOBJ.getObjApiName(), erpCenterId);
        if (specificationPloyDetail != null) {
            DetailObjectMappingsData.DetailObjectMappingData specValueObjectMapping = null;
            DetailObjectMappingsData detailObjectMappings = specificationPloyDetail.getDetailObjectMappings();
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : detailObjectMappings) {
                if (ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName().equals(detailObjectMappingData.getDestObjectApiName())) {
                    specValueObjectMapping = detailObjectMappingData;
                }
            }
            if (specValueObjectMapping != null) {
                erpSpecValueFakeObjApiName = specValueObjectMapping.getSourceObjectApiName();
            }
        }
        return erpSpecValueFakeObjApiName;
    }

    private void buildDoWriteResultData( SyncDataContextEvent message, int errCode, String errMsg) {
        SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
        writeResult.setSyncDataId(message.getSyncDataId());
        writeResult.setErrCode(errCode);
        writeResult.setErrMsg(errMsg);
        message.setWriteResult(writeResult);
        message.setStop(true);
    }

    private SyncDataContextEvent buildSuccessResult(SyncDataContextEvent message) {
        //产品id
        String productId = message.getDestData().getId();

        SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
        writeResult.setSyncDataId(message.getSyncDataId());
        writeResult.setDestDataId(productId);
        message.setWriteResult(writeResult);
        message.setStop(true);
        return message;
    }

    private SyncDataContextEvent buildErrorResult(SyncDataContextEvent message, String errMsg) {
        SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
        writeResult.setSyncDataId(message.getSyncDataId());
        writeResult.setErrCode(-1);
        writeResult.setErrMsg(errMsg);
        message.setWriteResult(writeResult);
        message.setStop(true);
        return message;
    }


    public Result<ObjectData> buildSpuObjectDataData(String tenantId, SyncDataContextEvent productMessage, SyncDataContextEvent erpObjDataResult,
                                                     SyncPloyDetailEntity spuPloyDetailEntity, String erpSpecValueFakeObjApiName,
                                                     ErpConnectInfoEntity connectInfo) {
        ObjectData sourceData = erpObjDataResult.getSourceData();
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(productMessage.getDestTenantId()), CrmConstants.SYSTEM_USER);
        ObjectData productObjectDataData = productMessage.getDestData();
        ObjectData spuObjectDataData = new ObjectData();
        spuObjectDataData.putTenantId(tenantId);
        spuObjectDataData.putApiName(ObjectApiNameEnum.FS_SPU.getObjApiName());
        FieldMappingsData spuFieldMappings = spuPloyDetailEntity.getFieldMappings();
        List<String> noUpdateField=Lists.newArrayList();
        for (FieldMappingData fieldMappingData : spuFieldMappings) {//字段转换
            if (BooleanUtils.isTrue(fieldMappingData.getNotUpdateField())) {
                noUpdateField.add(fieldMappingData.getDestApiName());
            }
            if(ErpFieldTypeEnum.category.name().equals(fieldMappingData.getSourceType())){//分类特殊处理
                specialFieldPreprocessManager.convertFieldValue2Category(connectInfo,sourceData.get(fieldMappingData.getSourceApiName()),
                        fieldMappingData.getSourceApiName(),sourceData,false);
            }
            Object value = doConvertValue(sourceData, 1, tenantId, fieldMappingData, TenantType.CRM, TenantType.ERP, ObjectApiNameEnum.FS_SPU.getObjApiName());
            spuObjectDataData.put(fieldMappingData.getDestApiName(), value);
        }
        //查询商品
        if (productMessage.getDestEventType() != null
                && EventTypeEnum.UPDATE.getType() == productMessage.getDestEventType()
                && StringUtils.isNotBlank(productMessage.getDestDataId())) {//通过产品id找商品id
            Result<com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult> product = objectDataService.getById(headerObj,
                    ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), productMessage.getDestDataId(), false);
            log.info("SyncSkuSpuServiceImpl.buildSpuObjectDataData,product={}",product);
            if (product.isSuccess() && product.getData() != null && product.getData().getObjectData() != null) {
                spuObjectDataData.putId(product.getData().getObjectData().get("spu_id").toString());
                for(String fieldKey:noUpdateField){
                    spuObjectDataData.remove(fieldKey);
                }
            }
            log.info("SyncSkuSpuServiceImpl.buildSpuObjectDataData,spu_id={}",spuObjectDataData.getId());
        } else {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.addFilter("name", Lists.newArrayList(String.valueOf(spuObjectDataData.get("name"))), "EQ");
            searchTemplateQuery.setPermissionType(0);
            Result<QueryBySearchTemplateResult> oldSpuDataResult = objectDataService.queryBySearchTemplate(headerObj, ObjectApiNameEnum.FS_SPU.getObjApiName(), searchTemplateQuery);
            log.info("objectDataService.queryBySearchTemplate searchTemplateQuery={} oldSpuDataResult={}", searchTemplateQuery, oldSpuDataResult);
            if (oldSpuDataResult != null && oldSpuDataResult.isSuccess() && oldSpuDataResult.getData() != null && oldSpuDataResult.getData().getQueryResult() != null
                    && CollectionUtils.isNotEmpty(oldSpuDataResult.getData().getQueryResult().getData())) {//找到名字一样的商品
                for(com.fxiaoke.crmrestapi.common.data.ObjectData oldSpuData:oldSpuDataResult.getData().getQueryResult().getData()){
                    if(String.valueOf(spuObjectDataData.get("name")).equals(String.valueOf(oldSpuData.get("name")))){//重新判断一次，因为查询接口忽略了大小写
                        spuObjectDataData.putId(oldSpuData.getId());
                    }
                }
            }
        }
        if(StringUtils.isEmpty(spuObjectDataData.getId()) && StringUtils.isNotEmpty(productMessage.getDestDataId())) {
            Result<com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult> product = objectDataService.getById(headerObj,
                    ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), productMessage.getDestDataId(), false);
            log.info("SyncSkuSpuServiceImpl.buildSpuObjectDataData,product2={}",product);
            if (product.isSuccess() && product.getData() != null && product.getData().getObjectData() != null) {
                spuObjectDataData.putId(product.getData().getObjectData().get("spu_id").toString());
            }
            log.info("SyncSkuSpuServiceImpl.buildSpuObjectDataData,spu_id2={}",spuObjectDataData.getId());
        }
        //是否有规格,默认为false
        spuObjectDataData.put("is_spec", false);
        if (erpSpecValueFakeObjApiName != null) {
            ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, erpSpecValueFakeObjApiName);
            if (idField == null) {
                throw new ErpSyncDataException(I18NStringEnum.s142,tenantId);
            }
            List<ErpObjectEntity> erpSpecValueObjs = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, Lists.newArrayList(erpSpecValueFakeObjApiName));
            List<ObjectData> dataList = Lists.newArrayList();
            for (ObjectData specValue : erpObjDataResult.getDetailData().get(erpSpecValueObjs.get(0).getErpObjectExtendValue())) {//找到id字段
                specValue.putId(String.valueOf(specValue.get(idField.getFieldApiName())));
                dataList.add(specValue);
            }
            erpObjDataResult.getDetailData().put(erpSpecValueObjs.get(0).getErpObjectExtendValue(), dataList);
            if (spuIsSpec(erpObjDataResult, erpSpecValueObjs.get(0).getErpObjectExtendValue())) {
                spuObjectDataData.put("is_spec", true);
                Result buildResult = buildSkuSpec(tenantId, erpObjDataResult, productObjectDataData, erpSpecValueFakeObjApiName, erpSpecValueObjs.get(0).getErpObjectExtendValue());
                if (!buildResult.isSuccess()) {
                    return buildResult;
                }
            }
        }
        //更新产品必传字段，并且字段的类型只能是数字
        productObjectDataData.put("status_flag", 1);
        spuObjectDataData.put("sku", Lists.newArrayList(productObjectDataData));
        //负责人
        if (CollectionUtils.isNotEmpty(productObjectDataData.getOwner())) {
            spuObjectDataData.putOwner(productObjectDataData.getOwner());
        } else {
            spuObjectDataData.putOwner(Lists.newArrayList(String.valueOf(CrmConstants.SYSTEM_USER)));
        }
        //固定字段的取产品上的字段
        spuObjectDataData.putIfAbsent("owner", productObjectDataData.getOwner());
        spuObjectDataData.putIfAbsent("standard_price", productObjectDataData.get("price"));
        spuObjectDataData.putIfAbsent("category", productObjectDataData.get("category"));
        spuObjectDataData.putIfAbsent("unit", productObjectDataData.get("unit"));
        spuObjectDataData.putIfAbsent("record_type", "default__c");
        log.info("buildSpuObjectDataData result={}", spuObjectDataData);
        Result result = new Result();
        result.setData(spuObjectDataData);
        return result;
    }

    private Result buildSkuSpec(String tenantId, SyncDataContextEvent erpObjDataResult, ObjectData productObjectDataData,
                                String erpSpecValueFakeObjApiName, String erpSpecValueRealObjApiName) {
        Result result = new Result();
        if (CollectionUtils.isNotEmpty(erpObjDataResult.getDetailData().get(erpSpecValueRealObjApiName))) {
            ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, erpSpecValueFakeObjApiName);
            String erpSpecValueIdFieldApiName = idField == null ? null : idField.getFieldApiName();
            HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
            for (ObjectData specValue : erpObjDataResult.getDetailData().get(erpSpecValueRealObjApiName)) {
                SyncDataMappingsEntity specificationMapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, erpSpecValueFakeObjApiName, ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName(), String.valueOf(specValue.get(erpSpecValueIdFieldApiName))
                );
                if (specificationMapping == null || !specificationMapping.getIsCreated()) {
                    result.setCode(-1);
                    result.setMessage(i18NStringManager.getByEi2(I18NStringEnum.s1275.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s1275.getI18nValue(), specValue.getId()),
                            Lists.newArrayList(specValue.getId())));
                    return result;
                }
                Result<ObjectDataGetByIdResult> objectDataResult = objectDataService.getById(headerObj, ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName(), specificationMapping.getDestDataId(), false);
                if (!objectDataResult.isSuccess()) {
                    result.setCode(objectDataResult.getCode());
                    result.setMessage(i18NStringManager.getByEi2(I18NStringEnum.s1276.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s1276.getI18nValue(), specificationMapping.getDestDataId()),
                            Lists.newArrayList(specificationMapping.getDestDataId())) + objectDataResult.getMessage());
                    return result;
                }
                com.fxiaoke.crmrestapi.common.data.ObjectData objectData = objectDataResult.getData().getObjectData();
                SpecAndValue specAndValue = new SpecAndValue();
                specAndValue.setSpecId(String.valueOf(objectData.get("specification_id")));
                specAndValue.setSpecValueId(objectData.getId());
                //产品规格
                if (productObjectDataData.get("spec_and_value") == null) {
                    specAndValue.setOrderField("1");
                    productObjectDataData.put("spec_and_value", Lists.newArrayList(specAndValue));
                } else {
                    specAndValue.setOrderField(String.valueOf(((List) productObjectDataData.get("spec_and_value")).size() + 1));
                    ((List) productObjectDataData.get("spec_and_value")).add(specAndValue);
                }
            }
        }
        return new Result();
    }

    private boolean spuIsSpec(SyncDataContextEvent erpObjDataResult, String erpSpecValueRealObjApiName) {
        if (erpObjDataResult != null && erpObjDataResult.getDetailData() != null && CollectionUtils.isNotEmpty(erpObjDataResult.getDetailData().get(erpSpecValueRealObjApiName))) {
            return true;
        }
        return false;
    }

    /**
     * 目前一个策略只能有一个策略明细
     *
     * @param type
     * @param tenantId
     * @param objApiName
     * @return
     */
    public SyncPloyDetailEntity getPloyDetailByTypeAndTenantIdAndObjApiName(Integer type, String tenantId, String objApiName, String erpDcId) {
        List<SyncPloyDetailEntity> ployDetailEntities;
        if (type == SyncPloyTypeEnum.OUTPUT.getType()) {//CRM往ERP
            ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listBySourceTenantTypeAndObjApiName(tenantId, TenantType.CRM, objApiName);
            ployDetailEntities.removeIf(v -> !erpDcId.equals(v.getDestDataCenterId()));
        } else {//ERP往CRM
            ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listByDestTenantTypeAndObjApiName(tenantId, TenantType.CRM, objApiName);
            ployDetailEntities.removeIf(v -> !erpDcId.equals(v.getSourceDataCenterId()));
        }
        if (CollectionUtils.isNotEmpty(ployDetailEntities)) {
            return ployDetailEntities.get(0);
        }
        return null;
    }

    public Object doConvertValue(ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMapping, Integer destTenantType, Integer sourceTenantType, String destObjectApiName) {
        FieldConverter fieldConverter = fieldConvertFactory.getConverter(fieldMapping.getMappingType());
        try {
            com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData fieldMappingData = new com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData();
            BeanUtils.copyProperties(fieldMapping, fieldMappingData, "optionMappings");
            if (CollectionUtils.isNotEmpty(fieldMapping.getOptionMappings())) {
                List<com.fxiaoke.open.erpsyncdata.preprocess.data.OptionMappingData> optionMappings = Lists.newArrayList();
                for (OptionMappingData optionMappingData : fieldMapping.getOptionMappings()) {
                    com.fxiaoke.open.erpsyncdata.preprocess.data.OptionMappingData optionMapping = new com.fxiaoke.open.erpsyncdata.preprocess.data.OptionMappingData();
                    BeanUtils.copyProperties(optionMappingData, optionMapping);
                    optionMappings.add(optionMapping);
                }
                fieldMappingData.setOptionMappings(optionMappings);
            }
            return fieldConverter.convert(destTenantId, sourceData, destEventType, destTenantId, fieldMappingData, destTenantType, sourceTenantType, destObjectApiName);
        } catch (RuntimeException e) {
            log.warn("sourceData={},fieldMappingData={}", sourceData, fieldMapping);
            throw e;
        }
    }

    public SyncDataContextEvent doWrite(SyncDataContextEvent syncDataContextEvent) {

        Integer destTenantId = Integer.valueOf(syncDataContextEvent.getDestTenantId());
        HeaderObj headerObj = new HeaderObj(destTenantId, CrmConstants.SYSTEM_USER);
        String destObjectApiName = syncDataContextEvent.getDestObjectApiName();
        String syncDataId = syncDataContextEvent.getSyncDataId();
        SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(syncDataContextEvent.getDestTenantId(), syncDataContextEvent.getSyncPloyDetailSnapshotId()).getData().getSyncPloyDetailData();

        if (syncDataContextEvent.getDestEventType() == EventTypeEnum.ADD.getType()) {
            Map<String, ObjectData> detailMaps = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
            Map<String, List<String>> detailSyncDataIdsMap = new HashedMap();
            Map<String, List<com.fxiaoke.crmrestapi.common.data.ObjectData>> detailDatasMap = new HashedMap();
            if (detailMaps != null) {

                for (Map.Entry<String, ObjectData> detailDataEntry : detailMaps.entrySet()) {
                    ObjectData detailData = detailDataEntry.getValue();
                    String detailObjectApiName = detailData.getApiName();
                    List<String> detailSyncDataIds = detailSyncDataIdsMap.get(detailObjectApiName);
                    if (detailSyncDataIds == null) {
                        detailSyncDataIds = new ArrayList<>();
                        detailSyncDataIdsMap.put(detailObjectApiName, detailSyncDataIds);
                    }
                    detailSyncDataIds.add(detailDataEntry.getKey());
                    List<com.fxiaoke.crmrestapi.common.data.ObjectData> detailDataList = detailDatasMap.get(detailObjectApiName);
                    if (detailDataList == null) {
                        detailDataList = new ArrayList<>();
                        detailDatasMap.put(detailObjectApiName, detailDataList);
                    }
                    com.fxiaoke.crmrestapi.common.data.ObjectData fsObjectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
                    fsObjectData.putAll(detailDataEntry.getValue());
                    detailDataList.add(fsObjectData);
                }
            }
            com.fxiaoke.crmrestapi.common.data.ObjectData fsObjectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
            fsObjectData.putAll(syncDataContextEvent.getDestData());
            Result<ActionAddResult> result = this.createCrmObjectData(headerObj, fsObjectData, detailDatasMap, syncDataContextEvent);
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            if (result.isSuccess()) {
                String id = result.getData().getObjectData().getId();
                writeResult.setSyncDataId(syncDataId);
                writeResult.setDestDataId(id);
                Map<String, List<com.fxiaoke.crmrestapi.common.data.ObjectData>> detailDataResultsMap = result.getData().getDetails();
                if (result.getData() != null) {
                    needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(syncDataContextEvent.getDestTenantId(), syncDataId, result.getData().getObjectData(), syncPloyDetailData);
                }
                if (detailDataResultsMap != null) {
                    Map<String, Map<String, String>> apiNameAndDestDataIdSyncDataIdMap = new HashMap<>();
                    for (Map.Entry<String, List<com.fxiaoke.crmrestapi.common.data.ObjectData>> detailEntry : detailDataResultsMap.entrySet()) {
                        String detailObjectApiName = detailEntry.getKey();
                        List<com.fxiaoke.crmrestapi.common.data.ObjectData> detailObjectResults = detailEntry.getValue();
                        List<String> syncDataIds = detailSyncDataIdsMap.get(detailObjectApiName);
                        if (detailObjectResults.size() != syncDataIds.size()) {
                            log.warn("detailObjectResults.size !=syncDataIds.size,detailObjectResults={},syncDataIds={}", detailObjectResults, syncDataIds);
                        }
                        Map<String, String> destDataIdSyncDataIdMap = apiNameAndDestDataIdSyncDataIdMap.computeIfAbsent(detailObjectApiName, k -> new HashMap<>());
                        for (int i = 0; i < detailObjectResults.size(); i++) {
                            SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                            com.fxiaoke.crmrestapi.common.data.ObjectData crmDetailObject = detailObjectResults.get(i);
                            detailWriteResult.setDestDataId(crmDetailObject.getId());
                            detailWriteResult.setSyncDataId(syncDataIds.get(i));
                            detailWriteResults.add(detailWriteResult);
                            destDataIdSyncDataIdMap.put(detailWriteResult.getDestDataId(), detailWriteResult.getSyncDataId());
                        }
                    }
                    needReturnData2SyncDataManager.doDetailNeedReturnData2SyncData(syncDataContextEvent.getDestTenantId(), detailDataResultsMap, apiNameAndDestDataIdSyncDataIdMap, syncPloyDetailData);
                }
            } else {
                ObjectDataGetByIdResult objectDataGetByIdResult = null;
                ObjectDataGetByIdResult detailObjectDataGetByIdResult = null;
                try {
                    objectDataGetByIdResult = objectDataService.getById(headerObj, destObjectApiName, syncDataContextEvent.getDestDataId(), false, false, false, false).getData();
                    if (detailMaps != null) {
                        for (String detailSyncDataId : detailMaps.keySet()) {
                            detailObjectDataGetByIdResult = objectDataService.getById(headerObj, destObjectApiName, syncDataContextEvent.getDestDataId(), false, false, false, false).getData();
                            if (detailObjectDataGetByIdResult != null && detailObjectDataGetByIdResult.getObjectData() != null) {
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取crm对象数据异常", e);
                }
                if ((objectDataGetByIdResult != null && objectDataGetByIdResult.getObjectData() != null) ||
                        (detailObjectDataGetByIdResult != null && detailObjectDataGetByIdResult.getObjectData() != null)) {
                    String id = objectDataGetByIdResult.getObjectData().getId();
                    writeResult.setSyncDataId(syncDataId);
                    writeResult.setDestDataId(id);
                    log.warn("创建数据失败，因为数据已存在，objectDataGetByIdResult=" + objectDataGetByIdResult);
                } else {
                    writeResult.setSyncDataId(syncDataId);
                    writeResult.setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                    writeResult.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s951,destTenantId+"")+ result.getMessage());
                    if (detailMaps != null) {
                        for (String detailSyncDataId : detailMaps.keySet()) {
                            SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                            detailWriteResult.setSyncDataId(detailSyncDataId);
                            detailWriteResult.setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                            detailWriteResult.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s951,destTenantId+"") + result.getMessage());
                            detailWriteResults.add(detailWriteResult);
                        }
                    }
                }
            }
            writeResult.setDestDetailSyncDataIdAndDestDataMap(syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap());
            syncDataContextEvent.setWriteResult(writeResult);
            syncDataContextEvent.setDetailWriteResults(detailWriteResults);
        } else if (syncDataContextEvent.getDestEventType() == EventTypeEnum.INVALID.getType() || syncDataContextEvent.getDestEventType() == EventTypeEnum.DELETE_DIRECT.getType()) {
            Result result = null;
            try {
                if (syncDataContextEvent.getDestEventType() == EventTypeEnum.INVALID.getType()) {
                    ActionBulkInvalidArg actionBulkInvalidArg = new ActionBulkInvalidArg(destObjectApiName, Lists.newArrayList(syncDataContextEvent.getDestDataId()));
                    result = metadataActionService.bulkInvalid(headerObj, destObjectApiName, null, null, actionBulkInvalidArg);
                } else if (syncDataContextEvent.getDestEventType() == EventTypeEnum.DELETE_DIRECT.getType() && (syncDataContextEvent.getDestObjectApiName().equals(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ) || syncDataContextEvent
                        .getDestObjectApiName().equals(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ))) {
                    ObjectDataGetByIdResult objectDataGetByIdResult = objectDataService.getById(headerObj, destObjectApiName, syncDataContextEvent.getDestDataId(), false, false, false, false).getData();
                    if (objectDataGetByIdResult != null && objectDataGetByIdResult.getObjectData() != null) {
                        BulkDeleteArg actionBulkInvalidArg = new BulkDeleteArg();
                        actionBulkInvalidArg.setDataIds(Lists.newArrayList(syncDataContextEvent.getDestDataId()));
                        result = objectDataService.bulkDelete(headerObj, destObjectApiName, actionBulkInvalidArg, true);
                    } else {
                        result = new Result();
                        result.setCode(Result.SUCCESS_CODE);
                        result.setMessage("data is delete");
                    }

                }
            } catch (CrmBusinessException e) {
                result = new Result<>();
                result.setCode(5000000);
                result.setMessage(e.getMessage());
                log.warn(e.getMessage(), e);
            } catch (Exception e) {
                result = new Result<>();
                result.setCode(5000000);
                //由于展示有限，只打印最底层异常
                Throwable rootCause = ExceptionUtil.getRootCause(e);
                String stackTrace = null;
                if (rootCause != null) {
                    //将caused by 抛出
                    StringWriter sw = new StringWriter();
                    PrintWriter pw = new PrintWriter(sw);
                    rootCause.printStackTrace(pw);
                    stackTrace = sw.toString();
                }
                result.setMessage(i18NStringManager.getByEi(I18NStringEnum.s952,destTenantId+"")+",exception "+
                        Optional.ofNullable(stackTrace).map((s)->s.length()>500 ? s.substring(0,500): s).orElse(" null exception"));
                log.warn(e.getMessage(), e);
            }
            if (result.isSuccess()) {
                 syncDataContextEvent.newSuccess(syncDataContextEvent.getDestEventType(), syncDataId, syncDataContextEvent.getDestDataId());
            } else {
                syncDataContextEvent.newError(syncDataContextEvent.getDestEventType(), syncDataId, i18NStringManager.getByEi(I18NStringEnum.s951,destTenantId+"") + result.getMessage());
            }
        } else if (syncDataContextEvent.getDestEventType() == EventTypeEnum.UPDATE.getType()) {
            com.fxiaoke.crmrestapi.common.data.ObjectData objectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
            objectData.putAll(syncDataContextEvent.getDestData());
            Result<ActionEditResult> result = updateCrmObjectData(headerObj, destObjectApiName, objectData, syncDataContextEvent);
            if (result.isSuccess()) {
                if (result.getData() != null) {
                    needReturnData2SyncDataManager.doMasterNeedReturnData2SyncData(syncDataContextEvent.getDestTenantId(), syncDataId, result.getData().getObjectData(), syncPloyDetailData);
                }
                syncDataContextEvent.newSuccess(syncDataContextEvent.getDestEventType(), syncDataId, syncDataContextEvent.getDestDataId());
            } else {
                syncDataContextEvent.newError(syncDataContextEvent.getDestEventType(), syncDataId, i18NStringManager.getByEi(I18NStringEnum.s951,destTenantId+"") + result.getMessage());
            }
        } else {
            syncDataContextEvent.newError(syncDataContextEvent.getDestEventType(), syncDataId, CompleteDataWriteArg.UNSUPPORT_EVENT_TYPE, "");
        }

        return syncDataContextEvent;
    }

    public Result<ActionEditResult> updateCrmObjectData(HeaderObj headerObj, String objectApiName, com.fxiaoke.crmrestapi.common.data.ObjectData fsObjectData, SyncDataContextEvent doWriteMqData) {
        Result<ActionEditResult> result = null;
        long callTime = System.currentTimeMillis();
        try {
            ActionEditArg actionEditArg = new ActionEditArg();
            actionEditArg.setObjectData(fsObjectData);
            actionEditArg.setDetails(new HashMap<>());
            Boolean isSpecifyCreatedBy=false, isSpecifyTime=false;
            if(fsObjectData!=null){
                isSpecifyCreatedBy=fsObjectData.containsKey("created_by");
                isSpecifyTime=fsObjectData.containsKey("create_time");
            }
            log.info("SyncSkuSpuServiceImpl.updateCrmObjectData,actionEditArg={}", JSONObject.toJSONString(actionEditArg));
            result = metadataActionService.edit(headerObj, objectApiName,isSpecifyCreatedBy,isSpecifyTime, null, null, actionEditArg);
            if (result.isSuccess()) {
                log.info("SyncSkuSpuServiceImpl.updateCrmObjectData,update result={}", JSONObject.toJSONString(result));
            }
        } catch (CrmBusinessException e) {
            result = new Result<>();
            result.setCode(5000000);
            result.setMessage(e.getMessage());
            log.warn(e.getMessage(), e);
        } catch (Exception e) {
            result = new Result<>();
            result.setCode(5000000);
            //由于展示有限，只打印最底层异常
            Throwable rootCause = ExceptionUtil.getRootCause(e);
            String stackTrace = null;
            if (rootCause != null) {
                //将caused by 抛出
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                rootCause.printStackTrace(pw);
                stackTrace = sw.toString();
            }
            result.setMessage(i18NStringManager.getByEi(I18NStringEnum.s952,doWriteMqData.getTenantId())+",exception "+
                    Optional.ofNullable(stackTrace).map((s)->s.length()>500 ? s.substring(0,500): s).orElse(" null exception"));
            log.warn(e.getMessage(), e);
        }
        return result;
    }


    public Result<ActionAddResult> createCrmObjectData(HeaderObj headerObj, com.fxiaoke.crmrestapi.common.data.ObjectData fsObjectData, Map<String, List<com.fxiaoke.crmrestapi.common.data.ObjectData>> detailDatasMap, SyncDataContextEvent doWriteMqData) {
        Result<ActionAddResult> result = null;
        long callTime = System.currentTimeMillis();
        try {
            Boolean isSpecifyCreatedBy=false, isSpecifyTime=false;
            if(fsObjectData!=null){
                isSpecifyCreatedBy=fsObjectData.containsKey("created_by");
                isSpecifyTime=fsObjectData.containsKey("create_time");
            }
            ActionAddArg addArg = new ActionAddArg();
            addArg.setObjectData(fsObjectData);
            addArg.setDetails(detailDatasMap);
            log.info("SyncSkuSpuServiceImpl.createCrmObjectData,addArg={}", JSONObject.toJSONString(addArg));
            result = metadataActionService.add(headerObj, fsObjectData.getApiName(),isSpecifyCreatedBy,isSpecifyTime, null, null, addArg);
        } catch (CrmBusinessException e) {
            result = new Result<>();
            result.setCode(5000000);
            result.setMessage(e.getMessage());
            log.warn(e.getMessage(), e);
        } catch (Exception e) {
            result = new Result<>();
            result.setCode(5000000);
            //由于展示有限，只打印最底层异常
            Throwable rootCause = ExceptionUtil.getRootCause(e);
            String stackTrace = null;
            if (rootCause != null) {
                //将caused by 抛出
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                rootCause.printStackTrace(pw);
                stackTrace = sw.toString();
            }
            result.setMessage(i18NStringManager.getByEi(I18NStringEnum.s952,doWriteMqData.getTenantId())+",exception "+
                    Optional.ofNullable(stackTrace).map((s)->s.length()>500 ? s.substring(0,500): s).orElse(" null exception"));
            log.warn(e.getMessage(), e);
        }
        return result;
    }

    public Result<String> updateSpuObj(Integer ei, ObjectData spuObjData) {
        Result<String> result = new Result<>();
        CrmRequestBaseParam baseParam = CrmRequestBaseParam.build(ei, null, ObjectApiNameEnum.FS_SPU.getObjApiName());
        Map<String, Object> objectDataMap = new HashMap<>();
        objectDataMap.put("object_data", spuObjData);
        log.info("SyncSkuSpuServiceImpl.updateSpuObj,objectDataMap={}", JSONObject.toJSONString(objectDataMap));
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<CrmObjectDataResult> remoteResult = crmRemoteService.updateObjectData(baseParam, objectDataMap, Collections.EMPTY_MAP);
        if (!remoteResult.isSuccess()) {
            result.setCode(-1);
            result.setMessage(remoteResult.getErrMsg());
            return result;
        }
        CrmObjectDataResult crmObjectDataResult = remoteResult.getData();
        if (crmObjectDataResult != null) {
            result.setCode(crmObjectDataResult.getErrorCode());
            result.setMessage(crmObjectDataResult.getErrorMessage());
        }
        return result;
    }
}
